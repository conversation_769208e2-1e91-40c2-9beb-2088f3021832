# 📝 دليل التعليقات والشرح للأكواد - خدمتي بلاس

## 🌟 **مقدمة**

هذا الدليل يحتوي على شرح شامل لجميع الأكواد المستخدمة في مشروع قاعدة بيانات "خدمتي بلاس" باللغة العربية، مع تفسير مفصل لكل جزء من الكود وغرضه.

---

## 📋 **فهرس المحتويات**

1. [🗄️ شرح ملف إنشاء قاعدة البيانات](#شرح-ملف-إنشاء-قاعدة-البيانات)
2. [🔍 شرح ملف الاستعلامات](#شرح-ملف-الاستعلامات)
3. [📊 شرح أنواع البيانات](#شرح-أنواع-البيانات)
4. [🔗 شرح العلاقات والقيود](#شرح-العلاقات-والقيود)
5. [📈 شرح الفهارس](#شرح-الفهارس)
6. [🎯 أمثلة عملية](#أمثلة-عملية)

---

## 🗄️ **شرح ملف إنشاء قاعدة البيانات**

### 📌 **الهيكل العام للملف:**

```sql
-- =====================================================
-- 🗄️ سكريبت إنشاء قاعدة بيانات خدمتي بلاس
-- =====================================================
```

**الشرح:**
- **الخطوط المتقطعة (=====)**: تستخدم لتقسيم الملف إلى أقسام واضحة
- **الرموز التعبيرية (🗄️)**: تساعد في التعرف السريع على نوع القسم
- **التعليقات العربية**: توضح الغرض من كل قسم

### 📌 **إنشاء قاعدة البيانات:**

```sql
CREATE DATABASE IF NOT EXISTS khedmaty_plus 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

**الشرح:**
- **`IF NOT EXISTS`**: يمنع حدوث خطأ إذا كانت قاعدة البيانات موجودة مسبقاً
- **`CHARACTER SET utf8mb4`**: يدعم جميع الأحرف العربية والرموز التعبيرية
- **`COLLATE utf8mb4_unicode_ci`**: يضمن الترتيب الصحيح للنصوص العربية

### 📌 **هيكل الجداول:**

#### **المعرفات الفريدة (Primary Keys):**
```sql
id VARCHAR(36) PRIMARY KEY
```

**الشرح:**
- **`VARCHAR(36)`**: يستوعب UUID بطول 36 حرف
- **`PRIMARY KEY`**: يضمن فرادة كل سجل في الجدول
- **UUID**: معرف فريد عالمياً، أكثر أماناً من الأرقام التسلسلية

#### **الحقول الإجبارية:**
```sql
full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل للمستخدم'
```

**الشرح:**
- **`NOT NULL`**: يمنع ترك الحقل فارغاً
- **`COMMENT`**: يوضح الغرض من الحقل باللغة العربية
- **`VARCHAR(100)`**: نص متغير الطول بحد أقصى 100 حرف

#### **الحقول الاختيارية:**
```sql
profile_image VARCHAR(255) NULL COMMENT 'رابط صورة الملف الشخصي'
```

**الشرح:**
- **`NULL`**: يسمح بترك الحقل فارغاً
- **`VARCHAR(255)`**: طول كافي لروابط الصور

### 📌 **أنواع البيانات المتخصصة:**

#### **القوائم المحددة (ENUM):**
```sql
role ENUM('client', 'service_provider') NOT NULL COMMENT 'دور المستخدم'
```

**الشرح:**
- **`ENUM`**: يحدد قائمة ثابتة من القيم المسموحة
- **يوفر الذاكرة**: أكثر كفاءة من VARCHAR للقيم الثابتة
- **يمنع الأخطاء**: لا يسمح بإدخال قيم غير صحيحة

#### **الأرقام العشرية:**
```sql
rating DECIMAL(3,2) DEFAULT 0.00 COMMENT 'متوسط التقييم (0.00-5.00)'
```

**الشرح:**
- **`DECIMAL(3,2)`**: 3 أرقام إجمالية، منها 2 بعد الفاصلة
- **`DEFAULT 0.00`**: القيمة الافتراضية عند الإنشاء
- **مناسب للتقييمات**: يدعم قيم من 0.00 إلى 5.00

#### **البيانات المنظمة (JSON):**
```sql
availability_hours JSON NULL COMMENT 'ساعات التوفر للعمل (JSON)'
```

**الشرح:**
- **`JSON`**: يخزن بيانات منظمة بصيغة JSON
- **مرونة عالية**: يمكن تخزين هياكل معقدة
- **قابل للاستعلام**: يدعم MySQL استعلامات JSON

### 📌 **الطوابع الزمنية:**

```sql
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الحساب',
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'
```

**الشرح:**
- **`TIMESTAMP`**: يخزن التاريخ والوقت بدقة
- **`DEFAULT CURRENT_TIMESTAMP`**: يسجل وقت الإنشاء تلقائياً
- **`ON UPDATE CURRENT_TIMESTAMP`**: يحدث الوقت عند أي تعديل

---

## 🔍 **شرح ملف الاستعلامات**

### 📌 **هيكل الاستعلامات:**

#### **استعلامات التقارير:**
```sql
-- 1. إحصائيات عامة للتطبيق
-- الغرض: عرض نظرة عامة سريعة على أداء التطبيق
-- الاستخدام: لوحة التحكم الإدارية والتقارير اليومية
SELECT 
    (SELECT COUNT(*) FROM users WHERE role = 'client') as total_clients,
    (SELECT COUNT(*) FROM users WHERE role = 'service_provider') as total_providers
```

**الشرح:**
- **التعليقات التوضيحية**: تشرح الغرض والاستخدام
- **الاستعلامات الفرعية**: تحسب إحصائيات مختلفة في استعلام واحد
- **الأسماء الواضحة**: أسماء الأعمدة توضح المحتوى

#### **استعلامات البحث:**
```sql
WHERE u.role = 'service_provider'                                                  -- مقدمي الخدمات فقط
  AND u.is_active = TRUE                                                           -- النشطين فقط
GROUP BY u.id                                                                      -- تجميع حسب المستخدم
```

**الشرح:**
- **شروط متعددة**: تصفية دقيقة للبيانات
- **تعليقات سطرية**: توضح كل شرط
- **التجميع**: لحساب الإحصائيات لكل مستخدم

### 📌 **أنواع الاستعلامات:**

#### **استعلامات القراءة (SELECT):**
- **الغرض**: استخراج البيانات للعرض
- **الاستخدام**: واجهات التطبيق، التقارير
- **الأمان**: آمنة، لا تغير البيانات

#### **استعلامات التحديث (UPDATE):**
```sql
UPDATE users u
SET 
    rating = (SELECT COALESCE(AVG(rating), 0) FROM ratings WHERE rated_id = u.id),
    total_ratings = (SELECT COUNT(*) FROM ratings WHERE rated_id = u.id)
WHERE u.role = 'service_provider';
```

**الشرح:**
- **`UPDATE`**: يحدث البيانات الموجودة
- **`COALESCE`**: يعطي قيمة افتراضية إذا كانت النتيجة NULL
- **استعلامات فرعية**: تحسب القيم الجديدة ديناميكياً

#### **استعلامات الحذف (DELETE):**
```sql
DELETE FROM auth_tokens 
WHERE expires_at < NOW() OR is_revoked = TRUE;
```

**الشرح:**
- **`DELETE`**: يحذف السجلات
- **شروط الأمان**: يحذف الرموز المنتهية فقط
- **`NOW()`**: الوقت الحالي للمقارنة

---

## 📊 **شرح أنواع البيانات**

### 📌 **النصوص:**

| النوع | الاستخدام | المثال |
|-------|-----------|---------|
| `VARCHAR(n)` | نصوص متغيرة الطول | الأسماء، العناوين |
| `TEXT` | نصوص طويلة | الأوصاف، المراجعات |
| `CHAR(n)` | نصوص ثابتة الطول | رموز البلدان |

### 📌 **الأرقام:**

| النوع | الاستخدام | المثال |
|-------|-----------|---------|
| `INT` | أرقام صحيحة | العدادات، المعرفات |
| `DECIMAL(m,d)` | أرقام عشرية دقيقة | الأسعار، التقييمات |
| `FLOAT` | أرقام عشرية تقريبية | الإحداثيات |

### 📌 **التواريخ:**

| النوع | الاستخدام | المثال |
|-------|-----------|---------|
| `DATE` | تاريخ فقط | تاريخ الميلاد |
| `TIME` | وقت فقط | ساعات العمل |
| `TIMESTAMP` | تاريخ ووقت | طوابع الإنشاء |
| `DATETIME` | تاريخ ووقت | المواعيد |

### 📌 **البيانات المنطقية:**

```sql
is_active BOOLEAN DEFAULT TRUE
```

**الشرح:**
- **`BOOLEAN`**: قيم صحيح/خطأ فقط
- **`TRUE/FALSE`**: القيم المسموحة
- **مفيد للحالات**: نشط/غير نشط، مقروء/غير مقروء

---

## 🔗 **شرح العلاقات والقيود**

### 📌 **المفاتيح الخارجية:**

```sql
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
```

**الشرح:**
- **`FOREIGN KEY`**: يربط جدولين ببعضهما
- **`REFERENCES`**: يشير إلى الجدول والحقل المرجعي
- **`ON DELETE CASCADE`**: يحذف السجلات المرتبطة عند حذف المرجع

### 📌 **أنواع العلاقات:**

#### **واحد لواحد (1:1):**
```sql
users (1) ←→ (1) user_profiles
```
- **مثال**: كل مستخدم له ملف شخصي واحد فقط

#### **واحد لمتعدد (1:N):**
```sql
users (1) ←→ (N) service_requests
```
- **مثال**: مستخدم واحد يمكن أن يكون له عدة طلبات

#### **متعدد لمتعدد (N:N):**
```sql
users (N) ←→ (N) conversations
```
- **مثال**: المستخدمون يشاركون في محادثات متعددة

### 📌 **قيود الحذف:**

| القيد | الوصف | الاستخدام |
|-------|--------|-----------|
| `CASCADE` | حذف السجلات المرتبطة | البيانات التابعة |
| `SET NULL` | تعيين NULL للمراجع | العلاقات الاختيارية |
| `RESTRICT` | منع الحذف | البيانات المهمة |

---

## 📈 **شرح الفهارس**

### 📌 **أنواع الفهارس:**

#### **الفهرس الأساسي:**
```sql
INDEX idx_email (email) COMMENT 'فهرس البريد الإلكتروني'
```

**الشرح:**
- **يسرع البحث**: يقلل وقت الاستعلام
- **يستهلك مساحة**: يحتاج ذاكرة إضافية
- **مفيد للبحث المتكرر**: الحقول المستخدمة في WHERE

#### **الفهرس المركب:**
```sql
INDEX idx_coordinates (latitude, longitude)
```

**الشرح:**
- **عدة حقول**: فهرس واحد لحقلين أو أكثر
- **ترتيب مهم**: الحقل الأول أكثر أهمية
- **مفيد للبحث الجغرافي**: البحث بالإحداثيات

#### **الفهرس الفريد:**
```sql
UNIQUE KEY unique_token (token)
```

**الشرح:**
- **يمنع التكرار**: لا يسمح بقيم متكررة
- **يسرع البحث**: مثل الفهرس العادي
- **مفيد للمعرفات**: الرموز، البريد الإلكتروني

---

## 🎯 **أمثلة عملية**

### 📌 **مثال 1: البحث عن مقدم خدمة:**

```sql
-- البحث عن كهربائي في مدينة إب
SELECT 
    u.full_name,                    -- اسم مقدم الخدمة
    u.phone,                        -- رقم الهاتف للتواصل
    u.rating,                       -- تقييم مقدم الخدمة
    up.experience_years             -- سنوات الخبرة
FROM users u                        -- جدول المستخدمين الأساسي
JOIN user_profiles up ON u.id = up.user_id  -- ربط مع الملف الشخصي
WHERE u.service_type = 'electrician'        -- نوع الخدمة: كهربائي
  AND up.city = 'إب'                        -- المدينة: إب
  AND u.is_active = TRUE                    -- النشطين فقط
ORDER BY u.rating DESC;                     -- ترتيب حسب التقييم
```

### 📌 **مثال 2: إحصائيات مقدم الخدمة:**

```sql
-- حساب إحصائيات مقدم خدمة معين
SELECT 
    COUNT(*) as total_requests,                                    -- إجمالي الطلبات
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed, -- الطلبات المكتملة
    ROUND(AVG(final_price), 2) as avg_price,                      -- متوسط السعر
    SUM(final_price) as total_earnings                             -- إجمالي الأرباح
FROM service_requests 
WHERE service_provider_id = 'PROVIDER_ID'                         -- معرف مقدم الخدمة
  AND status = 'completed';                                       -- المكتملة فقط
```

### 📌 **مثال 3: تنظيف البيانات:**

```sql
-- حذف الرموز المنتهية الصلاحية
DELETE FROM auth_tokens 
WHERE expires_at < NOW()           -- انتهت صلاحيتها
   OR is_revoked = TRUE;           -- أو تم إلغاؤها
   
-- تحديث إحصائيات المستخدمين
UPDATE users 
SET rating = (
    SELECT AVG(rating) 
    FROM ratings 
    WHERE rated_id = users.id      -- حساب متوسط التقييم
)
WHERE role = 'service_provider';   -- مقدمي الخدمات فقط
```

---

## 🎉 **الخلاصة**

هذا الدليل يوفر شرحاً شاملاً لجميع أجزاء الكود المستخدم في مشروع "خدمتي بلاس":

### ✨ **الفوائد الرئيسية:**
- **فهم عميق**: شرح مفصل لكل جزء من الكود
- **أمثلة عملية**: تطبيقات واقعية للاستعلامات
- **أفضل الممارسات**: استخدام التعليقات والتوثيق
- **سهولة الصيانة**: كود موثق وواضح

### 🚀 **الاستخدام الأمثل:**
- **للمطورين الجدد**: فهم سريع للمشروع
- **للصيانة**: تعديل الكود بثقة
- **للتطوير**: إضافة ميزات جديدة
- **للتدريب**: تعلم أفضل الممارسات

**📝 كود موثق بالكامل باللغة العربية لسهولة الفهم والصيانة! ✨**
