# 🗄️ تصميم قاعدة البيانات - خدمتي بلاس

## 🌟 **نظرة عامة**

تم تصميم قاعدة بيانات شاملة ومتطورة لتطبيق **"خدمتي بلاس"** لدعم جميع الوظائف والميزات المطلوبة مع ضمان الأداء العالي والأمان.

---

## 🎯 **متطلبات النظام المحللة**

### 👥 **إدارة المستخدمين:**
- تسجيل وتسجيل دخول العملاء ومقدمي الخدمات
- ملفات شخصية مفصلة مع صور وتقييمات
- نظام أدوار وصلاحيات متقدم
- مصادقة آمنة مع tokens

### 🛠️ **إدارة الخدمات:**
- 8 أنواع خدمات مختلفة (سائق، كهربائي، سباك، إلخ)
- طلبات خدمة مع حالات متعددة
- تتبع الطلبات في الوقت الفعلي
- نظام حجوزات متقدم

### 💰 **إدارة المدفوعات:**
- دفع نقدي وإلكتروني
- تتبع الأرباح والمصروفات
- فواتير مفصلة
- عمولات النظام

### ⭐ **التقييمات والمراجعات:**
- تقييم مقدمي الخدمات
- مراجعات نصية مفصلة
- نظام سمعة متقدم

### 💬 **التواصل:**
- رسائل مباشرة بين المستخدمين
- إشعارات فورية
- سجل محادثات

### 📍 **الموقع والتتبع:**
- مواقع جغرافية دقيقة
- تتبع الخدمات المتحركة
- مناطق خدمة محددة

---

## 🏗️ **هيكل قاعدة البيانات**

### 📊 **إحصائيات القاعدة:**
- **15 جدول رئيسي**
- **50+ حقل مفهرس**
- **دعم كامل للـ UTF-8**
- **تشفير البيانات الحساسة**

---

## 📋 **الجداول الرئيسية**

### 1. 👤 **جدول المستخدمين (users)**
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('client', 'service_provider') NOT NULL,
    service_type ENUM('driver', 'electrician', 'plumber', 'carpenter', 
                     'mechanic', 'cleaner', 'painter', 'gardener') NULL,
    profile_image VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_service_type (service_type),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
);
```

### 2. 🔐 **جدول المصادقة (auth_tokens)**
```sql
CREATE TABLE auth_tokens (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_type ENUM('access', 'refresh', 'reset_password') NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at)
);
```

### 3. 📝 **جدول معلومات إضافية (user_profiles)**
```sql
CREATE TABLE user_profiles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    date_of_birth DATE NULL,
    gender ENUM('male', 'female') NULL,
    national_id VARCHAR(50) NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    district VARCHAR(50) NULL,
    bio TEXT NULL,
    experience_years INT NULL,
    certifications JSON NULL,
    languages JSON NULL,
    availability_hours JSON NULL,
    emergency_contact JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_city (city),
    INDEX idx_district (district)
);
```

### 4. 🛠️ **جدول الخدمات (services)**
```sql
CREATE TABLE services (
    id VARCHAR(36) PRIMARY KEY,
    service_type ENUM('driver', 'electrician', 'plumber', 'carpenter', 
                     'mechanic', 'cleaner', 'painter', 'gardener') NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    base_price DECIMAL(10,2) DEFAULT 0.00,
    price_per_hour DECIMAL(10,2) NULL,
    price_per_km DECIMAL(10,2) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_service_type (service_type),
    INDEX idx_is_active (is_active)
);
```

### 5. 📋 **جدول طلبات الخدمة (service_requests)**
```sql
CREATE TABLE service_requests (
    id VARCHAR(36) PRIMARY KEY,
    client_id VARCHAR(36) NOT NULL,
    service_provider_id VARCHAR(36) NULL,
    service_id VARCHAR(36) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    status ENUM('pending', 'accepted', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    estimated_price DECIMAL(10,2) NULL,
    final_price DECIMAL(10,2) NULL,
    payment_method ENUM('cash', 'electronic') NOT NULL,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    scheduled_at TIMESTAMP NULL,
    accepted_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    cancellation_reason TEXT NULL,
    additional_info JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_provider_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT,
    
    INDEX idx_client_id (client_id),
    INDEX idx_service_provider_id (service_provider_id),
    INDEX idx_service_id (service_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_scheduled_at (scheduled_at)
);
```

### 6. 📍 **جدول المواقع (locations)**
```sql
CREATE TABLE locations (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    location_type ENUM('pickup', 'dropoff', 'service_location') NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT NOT NULL,
    landmark VARCHAR(200) NULL,
    building_number VARCHAR(20) NULL,
    floor_number VARCHAR(10) NULL,
    apartment_number VARCHAR(10) NULL,
    special_instructions TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    INDEX idx_request_id (request_id),
    INDEX idx_location_type (location_type),
    INDEX idx_coordinates (latitude, longitude)
);
```

### 7. 💰 **جدول المدفوعات (payments)**
```sql
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    payer_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    service_fee DECIMAL(10,2) DEFAULT 0.00,
    platform_commission DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'electronic', 'wallet') NOT NULL,
    payment_gateway VARCHAR(50) NULL,
    transaction_id VARCHAR(100) NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    refunded_at TIMESTAMP NULL,
    refund_reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (payer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_request_id (request_id),
    INDEX idx_payer_id (payer_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_status (status),
    INDEX idx_paid_at (paid_at)
);
```

### 8. ⭐ **جدول التقييمات (ratings)**
```sql
CREATE TABLE ratings (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    rater_id VARCHAR(36) NOT NULL,
    rated_id VARCHAR(36) NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT NULL,
    service_quality INT NULL CHECK (service_quality >= 1 AND service_quality <= 5),
    communication INT NULL CHECK (communication >= 1 AND communication <= 5),
    punctuality INT NULL CHECK (punctuality >= 1 AND punctuality <= 5),
    professionalism INT NULL CHECK (professionalism >= 1 AND professionalism <= 5),
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (rater_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rated_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_rating (request_id, rater_id, rated_id),
    INDEX idx_request_id (request_id),
    INDEX idx_rated_id (rated_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
);
```

### 9. 💬 **جدول المحادثات (conversations)**
```sql
CREATE TABLE conversations (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NULL,
    participant_1_id VARCHAR(36) NOT NULL,
    participant_2_id VARCHAR(36) NOT NULL,
    conversation_type ENUM('service_request', 'general', 'support') DEFAULT 'service_request',
    title VARCHAR(200) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE SET NULL,
    FOREIGN KEY (participant_1_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (participant_2_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_request_id (request_id),
    INDEX idx_participant_1 (participant_1_id),
    INDEX idx_participant_2 (participant_2_id),
    INDEX idx_last_message_at (last_message_at)
);
```

### 10. 📨 **جدول الرسائل (messages)**
```sql
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message_type ENUM('text', 'image', 'file', 'location', 'system') DEFAULT 'text',
    content TEXT NOT NULL,
    file_url VARCHAR(500) NULL,
    file_name VARCHAR(255) NULL,
    file_size INT NULL,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    reply_to_id VARCHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_id) REFERENCES messages(id) ON DELETE SET NULL,

    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
);
```

### 11. 🔔 **جدول الإشعارات (notifications)**
```sql
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type ENUM('new_request', 'request_accepted', 'request_rejected',
                          'service_completed', 'new_message', 'payment', 'general') NOT NULL,
    related_id VARCHAR(36) NULL,
    related_type ENUM('request', 'message', 'payment', 'user') NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);
```

### 12. 📱 **جدول أجهزة FCM (fcm_tokens)**
```sql
CREATE TABLE fcm_tokens (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    token VARCHAR(500) NOT NULL,
    device_type ENUM('android', 'ios', 'web') NOT NULL,
    device_id VARCHAR(100) NULL,
    app_version VARCHAR(20) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active)
);
```

### 13. 🗺️ **جدول تتبع الموقع (location_tracking)**
```sql
CREATE TABLE location_tracking (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(8, 2) NULL,
    speed DECIMAL(8, 2) NULL,
    heading DECIMAL(8, 2) NULL,
    altitude DECIMAL(8, 2) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_request_id (request_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_coordinates (latitude, longitude)
);
```

### 14. 📊 **جدول الإحصائيات (statistics)**
```sql
CREATE TABLE statistics (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    stat_type ENUM('daily_earnings', 'monthly_earnings', 'total_requests',
                   'completed_requests', 'cancelled_requests', 'rating_average') NOT NULL,
    stat_date DATE NOT NULL,
    value DECIMAL(15, 2) NOT NULL,
    additional_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_stat (user_id, stat_type, stat_date),
    INDEX idx_user_id (user_id),
    INDEX idx_stat_type (stat_type),
    INDEX idx_stat_date (stat_date)
);
```

### 15. ⚙️ **جدول إعدادات النظام (system_settings)**
```sql
CREATE TABLE system_settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);
```

---

## 🔗 **العلاقات بين الجداول**

### 🎯 **العلاقات الرئيسية:**

#### 👤 **المستخدمون:**
- `users` ← `user_profiles` (1:1)
- `users` ← `auth_tokens` (1:N)
- `users` ← `fcm_tokens` (1:N)
- `users` ← `statistics` (1:N)

#### 🛠️ **الخدمات والطلبات:**
- `users` ← `service_requests` (1:N) كعميل
- `users` ← `service_requests` (1:N) كمقدم خدمة
- `services` ← `service_requests` (1:N)
- `service_requests` ← `locations` (1:N)
- `service_requests` ← `payments` (1:1)
- `service_requests` ← `ratings` (1:N)
- `service_requests` ← `location_tracking` (1:N)

#### 💬 **التواصل:**
- `users` ← `conversations` (N:N)
- `service_requests` ← `conversations` (1:1)
- `conversations` ← `messages` (1:N)
- `users` ← `notifications` (1:N)

---

## 📊 **مخطط ERD**

```mermaid
erDiagram
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ AUTH_TOKENS : owns
    USERS ||--o{ FCM_TOKENS : has
    USERS ||--o{ SERVICE_REQUESTS : creates
    USERS ||--o{ SERVICE_REQUESTS : provides
    USERS ||--o{ CONVERSATIONS : participates
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ NOTIFICATIONS : receives
    USERS ||--o{ RATINGS : gives
    USERS ||--o{ RATINGS : receives
    USERS ||--o{ PAYMENTS : pays
    USERS ||--o{ PAYMENTS : receives
    USERS ||--o{ STATISTICS : has
    USERS ||--o{ LOCATION_TRACKING : tracks

    SERVICES ||--o{ SERVICE_REQUESTS : requested
    SERVICE_REQUESTS ||--o{ LOCATIONS : has
    SERVICE_REQUESTS ||--|| PAYMENTS : paid_by
    SERVICE_REQUESTS ||--o{ RATINGS : rated_by
    SERVICE_REQUESTS ||--|| CONVERSATIONS : discussed_in
    SERVICE_REQUESTS ||--o{ LOCATION_TRACKING : tracked_by

    CONVERSATIONS ||--o{ MESSAGES : contains
    MESSAGES ||--o{ MESSAGES : replies_to
```

---

## 🔍 **الاستعلامات الشائعة**

### 📊 **استعلامات التقارير:**

#### 1. **إحصائيات المستخدمين:**
```sql
-- عدد المستخدمين حسب النوع
SELECT role, COUNT(*) as user_count
FROM users
GROUP BY role;

-- أفضل مقدمي الخدمات حسب التقييم
SELECT full_name, service_type, rating, total_ratings
FROM users
WHERE role = 'service_provider'
ORDER BY rating DESC, total_ratings DESC
LIMIT 10;
```

#### 2. **إحصائيات الطلبات:**
```sql
-- الطلبات حسب الحالة
SELECT status, COUNT(*) as request_count
FROM service_requests
GROUP BY status;

-- الطلبات الشهرية
SELECT
    DATE_FORMAT(created_at, '%Y-%m') as month,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests
FROM service_requests
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY month DESC;
```

### 🔎 **استعلامات البحث:**

#### 1. **البحث عن مقدمي الخدمات:**
```sql
-- مقدمو الخدمات المتاحون حسب النوع والموقع
SELECT u.*, up.city, up.district
FROM users u
JOIN user_profiles up ON u.id = up.user_id
WHERE u.role = 'service_provider'
  AND u.service_type = 'electrician'
  AND u.is_active = TRUE
  AND up.city = 'إب'
ORDER BY u.rating DESC;
```

### 📱 **استعلامات التطبيق:**

#### 1. **تسجيل الدخول:**
```sql
-- التحقق من بيانات المستخدم
SELECT id, full_name, email, role, service_type, is_active, is_verified
FROM users
WHERE email = '<EMAIL>'
  AND password_hash = 'hashed_password'
  AND is_active = TRUE;
```

#### 2. **الإشعارات غير المقروءة:**
```sql
-- عدد الإشعارات غير المقروءة
SELECT COUNT(*) as unread_count
FROM notifications
WHERE user_id = 'user_id_here'
  AND is_read = FALSE;
```

---

## 🔧 **الإجراءات المخزنة**

### 1. **تحديث التقييم:**
```sql
CALL UpdateUserRating('user_id_here');
```

### 2. **إنشاء إشعار:**
```sql
CALL CreateNotification(
    'user_id_here',
    'طلب جديد',
    'لديك طلب خدمة جديد',
    'new_request',
    'request_id_here',
    'request',
    '{"priority": "high"}'
);
```

---

## 📈 **الفهارس والأداء**

### 🚀 **الفهارس المهمة:**
- **users**: email, phone, role, service_type, rating
- **service_requests**: client_id, service_provider_id, status, created_at
- **payments**: request_id, payer_id, receiver_id, status
- **messages**: conversation_id, sender_id, created_at
- **notifications**: user_id, is_read, created_at
- **location_tracking**: request_id, user_id, timestamp

### ⚡ **تحسينات الأداء:**
- استخدام UUID للمفاتيح الأساسية
- فهرسة الحقول المستخدمة في WHERE و JOIN
- تقسيم البيانات الكبيرة (Partitioning) للجداول الكبيرة
- استخدام المشاهد للاستعلامات المعقدة

---

## 🔒 **الأمان والخصوصية**

### 🛡️ **إجراءات الأمان:**
- تشفير كلمات المرور باستخدام bcrypt
- استخدام tokens للمصادقة
- تشفير البيانات الحساسة
- تسجيل العمليات الحساسة

### 🔐 **صلاحيات قاعدة البيانات:**
```sql
-- مستخدم التطبيق
CREATE USER 'app_user'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON khedmaty_plus.* TO 'app_user'@'%';

-- مستخدم القراءة فقط للتقارير
CREATE USER 'report_user'@'%' IDENTIFIED BY 'report_password';
GRANT SELECT ON khedmaty_plus.* TO 'report_user'@'%';
```

---

## 📊 **النسخ الاحتياطي والاستعادة**

### 💾 **النسخ الاحتياطي:**
```bash
# نسخة احتياطية كاملة
mysqldump -u backup_user -p khedmaty_plus > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 🔄 **الاستعادة:**
```bash
# استعادة قاعدة البيانات
mysql -u root -p khedmaty_plus < backup_file.sql
```

---

**🗄️ قاعدة بيانات متطورة وشاملة لتطبيق "خدمتي بلاس" جاهزة للاستخدام! ✨**
