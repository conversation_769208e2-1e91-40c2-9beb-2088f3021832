# 🎯 أمثلة عملية مع التعليقات - خدمتي بلاس

## 🌟 **مقدمة**

هذا الملف يحتوي على أمثلة عملية شاملة لاستخدام قاعدة بيانات "خدمتي بلاس" مع تعليقات مفصلة باللغة العربية لكل خطوة.

---

## 📋 **فهرس الأمثلة**

1. [👤 إدارة المستخدمين](#إدارة-المستخدمين)
2. [🛠️ إدارة الطلبات](#إدارة-الطلبات)
3. [💰 إدارة المدفوعات](#إدارة-المدفوعات)
4. [⭐ إدارة التقييمات](#إدارة-التقييمات)
5. [💬 إدارة المحادثات](#إدارة-المحادثات)
6. [📊 التقارير والإحصائيات](#التقارير-والإحصائيات)

---

## 👤 **إدارة المستخدمين**

### 🔐 **مثال 1: تسجيل مستخدم جديد**

```sql
-- خطوة 1: إنشاء حساب مستخدم جديد
-- الغرض: تسجيل عميل جديد في التطبيق
INSERT INTO users (
    id,                              -- معرف فريد للمستخدم
    full_name,                       -- الاسم الكامل
    email,                           -- البريد الإلكتروني (فريد)
    phone,                           -- رقم الهاتف (فريد)
    password_hash,                   -- كلمة المرور المشفرة
    role,                            -- دور المستخدم
    is_active,                       -- حالة النشاط
    created_at                       -- تاريخ التسجيل
) VALUES (
    UUID(),                          -- إنشاء معرف فريد تلقائياً
    'أحمد محمد علي',                 -- اسم المستخدم
    '<EMAIL>',             -- بريد إلكتروني صحيح
    '+************',                 -- رقم هاتف يمني
    SHA2('password123', 256),        -- تشفير كلمة المرور
    'client',                        -- تسجيل كعميل
    TRUE,                            -- تفعيل الحساب مباشرة
    NOW()                            -- الوقت الحالي
);

-- خطوة 2: إنشاء ملف شخصي للمستخدم
-- الغرض: إضافة معلومات تفصيلية للمستخدم
INSERT INTO user_profiles (
    id,                              -- معرف الملف الشخصي
    user_id,                         -- ربط بالمستخدم
    city,                            -- المدينة
    district,                        -- الحي
    bio,                             -- نبذة شخصية
    created_at                       -- تاريخ الإنشاء
) VALUES (
    UUID(),                          -- معرف فريد للملف
    LAST_INSERT_ID(),                -- معرف المستخدم المنشأ حديثاً
    'صنعاء',                         -- المدينة
    'الحصبة',                        -- الحي
    'عميل جديد يبحث عن خدمات منزلية', -- وصف مختصر
    NOW()                            -- الوقت الحالي
);
```

### 🔍 **مثال 2: البحث عن مقدم خدمة**

```sql
-- البحث عن أفضل كهربائي في صنعاء
-- الغرض: العثور على مقدم خدمة مناسب للعميل
SELECT 
    u.id,                                    -- معرف مقدم الخدمة
    u.full_name AS provider_name,            -- اسم مقدم الخدمة
    u.phone AS contact_phone,                -- رقم الهاتف للتواصل
    u.rating AS provider_rating,             -- تقييم مقدم الخدمة
    u.total_ratings AS rating_count,         -- عدد التقييمات
    up.city,                                 -- المدينة
    up.district,                             -- الحي
    up.bio AS provider_bio,                  -- نبذة عن مقدم الخدمة
    up.experience_years,                     -- سنوات الخبرة
    
    -- حساب إحصائيات الأداء
    COUNT(sr.id) AS total_services,          -- إجمالي الخدمات المقدمة
    COUNT(CASE WHEN sr.status = 'completed' THEN 1 END) AS completed_services, -- الخدمات المكتملة
    
    -- حساب معدل الإكمال
    ROUND(
        COUNT(CASE WHEN sr.status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(sr.id), 0), 2
    ) AS completion_rate,                    -- معدل إكمال الخدمات بالنسبة المئوية
    
    -- حساب متوسط السعر
    ROUND(AVG(CASE WHEN sr.status = 'completed' THEN sr.final_price END), 2) AS avg_price

FROM users u                                -- جدول المستخدمين الأساسي
INNER JOIN user_profiles up ON u.id = up.user_id  -- ربط مع الملفات الشخصية
LEFT JOIN service_requests sr ON u.id = sr.service_provider_id  -- ربط مع طلبات الخدمة

WHERE u.role = 'service_provider'           -- مقدمي الخدمات فقط
  AND u.service_type = 'electrician'        -- نوع الخدمة: كهربائي
  AND u.is_active = TRUE                    -- النشطين فقط
  AND u.is_verified = TRUE                  -- المتحققين فقط
  AND up.city = 'صنعاء'                     -- في مدينة صنعاء

GROUP BY u.id                               -- تجميع حسب مقدم الخدمة
HAVING COUNT(sr.id) >= 5                    -- الذين لديهم 5 خدمات على الأقل
ORDER BY u.rating DESC,                     -- ترتيب حسب التقييم (الأعلى أولاً)
         completion_rate DESC,              -- ثم معدل الإكمال
         total_services DESC                -- ثم عدد الخدمات
LIMIT 10;                                   -- أفضل 10 مقدمي خدمة
```

---

## 🛠️ **إدارة الطلبات**

### 📝 **مثال 3: إنشاء طلب خدمة جديد**

```sql
-- إنشاء طلب خدمة كهربائية
-- الغرض: عميل يطلب خدمة إصلاح كهربائي في منزله

-- خطوة 1: إنشاء الطلب الأساسي
INSERT INTO service_requests (
    id,                              -- معرف فريد للطلب
    client_id,                       -- معرف العميل
    service_id,                      -- معرف نوع الخدمة
    title,                           -- عنوان الطلب
    description,                     -- وصف تفصيلي للمشكلة
    status,                          -- حالة الطلب
    priority,                        -- أولوية الطلب
    payment_method,                  -- طريقة الدفع المفضلة
    scheduled_at,                    -- الموعد المطلوب
    created_at                       -- تاريخ إنشاء الطلب
) VALUES (
    UUID(),                          -- معرف فريد تلقائي
    'client-uuid-here',              -- معرف العميل (من الجلسة)
    (SELECT id FROM services WHERE service_type = 'electrician' LIMIT 1), -- معرف خدمة الكهربائي
    'إصلاح عطل في الكهرباء',          -- عنوان واضح للطلب
    'يوجد عطل في الكهرباء في غرفة النوم، الأنوار لا تعمل والمقابس لا تعطي كهرباء. المشكلة بدأت منذ أمس بعد انقطاع الكهرباء.', -- وصف مفصل
    'pending',                       -- حالة معلقة في انتظار مقدم خدمة
    'high',                          -- أولوية عالية (مشكلة كهربائية)
    'cash',                          -- الدفع نقداً
    DATE_ADD(NOW(), INTERVAL 2 HOUR), -- خلال ساعتين من الآن
    NOW()                            -- الوقت الحالي
);

-- خطوة 2: إضافة موقع الخدمة
INSERT INTO locations (
    id,                              -- معرف الموقع
    request_id,                      -- ربط بالطلب
    location_type,                   -- نوع الموقع
    latitude,                        -- خط العرض
    longitude,                       -- خط الطول
    address,                         -- العنوان التفصيلي
    landmark,                        -- معلم مميز
    building_number,                 -- رقم المبنى
    floor_number,                    -- رقم الطابق
    special_instructions,            -- تعليمات خاصة
    created_at                       -- تاريخ الإضافة
) VALUES (
    UUID(),                          -- معرف فريد للموقع
    LAST_INSERT_ID(),                -- معرف الطلب المنشأ حديثاً
    'service_location',              -- موقع تقديم الخدمة
    15.3694,                         -- خط عرض صنعاء
    44.1910,                         -- خط طول صنعاء
    'شارع الزبيري، حي الحصبة، صنعاء',  -- العنوان الكامل
    'بجانب مسجد النور',              -- معلم مميز للوصول
    '123',                           -- رقم المبنى
    '2',                             -- الطابق الثاني
    'الرجاء الاتصال عند الوصول. البوابة الرئيسية مغلقة، استخدم البوابة الجانبية.', -- تعليمات للوصول
    NOW()                            -- الوقت الحالي
);
```

### 🔄 **مثال 4: تحديث حالة الطلب**

```sql
-- قبول طلب من مقدم خدمة
-- الغرض: مقدم خدمة يقبل طلب معين ويبدأ العمل عليه

-- خطوة 1: تحديث الطلب بمعلومات مقدم الخدمة
UPDATE service_requests 
SET 
    service_provider_id = 'provider-uuid-here',  -- معرف مقدم الخدمة
    status = 'accepted',                         -- تغيير الحالة إلى مقبول
    accepted_at = NOW(),                         -- تسجيل وقت القبول
    estimated_price = 50.00,                     -- السعر المقدر للخدمة
    updated_at = NOW()                           -- تحديث طابع التعديل
WHERE id = 'request-uuid-here'                   -- معرف الطلب المحدد
  AND status = 'pending'                         -- التأكد أن الطلب ما زال معلقاً
  AND service_provider_id IS NULL;              -- والتأكد أنه لم يتم قبوله مسبقاً

-- خطوة 2: إنشاء إشعار للعميل
INSERT INTO notifications (
    id,                              -- معرف الإشعار
    user_id,                         -- معرف العميل المستقبل
    title,                           -- عنوان الإشعار
    message,                         -- نص الإشعار
    notification_type,               -- نوع الإشعار
    related_id,                      -- معرف الطلب المرتبط
    related_type,                    -- نوع العنصر المرتبط
    created_at                       -- تاريخ الإنشاء
) VALUES (
    UUID(),                          -- معرف فريد للإشعار
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'), -- معرف العميل
    'تم قبول طلبك',                  -- عنوان الإشعار
    'تم قبول طلب الخدمة الخاص بك من قبل مقدم خدمة. سيتم التواصل معك قريباً.', -- نص الإشعار
    'request_accepted',              -- نوع الإشعار
    'request-uuid-here',             -- معرف الطلب
    'request',                       -- نوع العنصر
    NOW()                            -- الوقت الحالي
);

-- خطوة 3: إنشاء محادثة بين العميل ومقدم الخدمة
INSERT INTO conversations (
    id,                              -- معرف المحادثة
    request_id,                      -- ربط بالطلب
    participant_1_id,                -- العميل
    participant_2_id,                -- مقدم الخدمة
    conversation_type,               -- نوع المحادثة
    title,                           -- عنوان المحادثة
    is_active,                       -- حالة النشاط
    created_at                       -- تاريخ الإنشاء
) VALUES (
    UUID(),                          -- معرف فريد للمحادثة
    'request-uuid-here',             -- معرف الطلب
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'), -- العميل
    'provider-uuid-here',            -- مقدم الخدمة
    'service_request',               -- محادثة خاصة بطلب خدمة
    'محادثة حول طلب الخدمة',          -- عنوان المحادثة
    TRUE,                            -- محادثة نشطة
    NOW()                            -- الوقت الحالي
);
```

---

## 💰 **إدارة المدفوعات**

### 💳 **مثال 5: معالجة دفعة**

```sql
-- معالجة دفعة عند إكمال الخدمة
-- الغرض: تسجيل دفعة من العميل لمقدم الخدمة بعد إكمال الخدمة

-- خطوة 1: إنشاء سجل الدفعة
INSERT INTO payments (
    id,                              -- معرف الدفعة
    request_id,                      -- معرف الطلب
    payer_id,                        -- معرف الدافع (العميل)
    receiver_id,                     -- معرف المستقبل (مقدم الخدمة)
    amount,                          -- المبلغ الإجمالي
    service_fee,                     -- رسوم الخدمة
    platform_commission,             -- عمولة المنصة (10%)
    net_amount,                      -- المبلغ الصافي لمقدم الخدمة
    payment_method,                  -- طريقة الدفع
    status,                          -- حالة الدفعة
    paid_at,                         -- وقت الدفع
    created_at                       -- تاريخ الإنشاء
) VALUES (
    UUID(),                          -- معرف فريد للدفعة
    'request-uuid-here',             -- معرف الطلب المكتمل
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'), -- العميل الدافع
    (SELECT service_provider_id FROM service_requests WHERE id = 'request-uuid-here'), -- مقدم الخدمة المستقبل
    75.00,                           -- المبلغ الإجمالي المتفق عليه
    5.00,                            -- رسوم معالجة الدفع
    7.50,                            -- عمولة المنصة (10% من 75)
    62.50,                           -- المبلغ الصافي (75 - 5 - 7.5)
    'cash',                          -- دفع نقدي
    'completed',                     -- دفعة مكتملة
    NOW(),                           -- وقت الدفع الحالي
    NOW()                            -- تاريخ الإنشاء
);

-- خطوة 2: تحديث حالة الطلب إلى مكتمل
UPDATE service_requests 
SET 
    status = 'completed',            -- تغيير الحالة إلى مكتمل
    completed_at = NOW(),            -- تسجيل وقت الإكمال
    final_price = 75.00,             -- السعر النهائي
    payment_status = 'paid',         -- حالة الدفع: مدفوع
    updated_at = NOW()               -- تحديث طابع التعديل
WHERE id = 'request-uuid-here'       -- معرف الطلب
  AND status = 'in_progress';        -- التأكد أن الطلب قيد التنفيذ

-- خطوة 3: إنشاء إشعارات للطرفين
-- إشعار للعميل
INSERT INTO notifications (
    id, user_id, title, message, notification_type, related_id, related_type, created_at
) VALUES (
    UUID(),
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'),
    'تم إكمال الخدمة',
    'تم إكمال الخدمة بنجاح. يمكنك الآن تقييم مقدم الخدمة.',
    'service_completed',
    'request-uuid-here',
    'request',
    NOW()
);

-- إشعار لمقدم الخدمة
INSERT INTO notifications (
    id, user_id, title, message, notification_type, related_id, related_type, created_at
) VALUES (
    UUID(),
    (SELECT service_provider_id FROM service_requests WHERE id = 'request-uuid-here'),
    'تم استلام الدفعة',
    'تم استلام دفعة بقيمة 62.50 ريال عن الخدمة المكتملة.',
    'payment',
    'request-uuid-here',
    'payment',
    NOW()
);
```

---

## ⭐ **إدارة التقييمات**

### 📊 **مثال 6: إضافة تقييم**

```sql
-- إضافة تقييم من العميل لمقدم الخدمة
-- الغرض: العميل يقيم جودة الخدمة المقدمة

INSERT INTO ratings (
    id,                              -- معرف التقييم
    request_id,                      -- معرف الطلب المقيم
    rater_id,                        -- معرف المقيم (العميل)
    rated_id,                        -- معرف المقيم (مقدم الخدمة)
    rating,                          -- التقييم العام (1-5)
    review,                          -- مراجعة نصية
    service_quality,                 -- تقييم جودة الخدمة
    communication,                   -- تقييم التواصل
    punctuality,                     -- تقييم الالتزام بالوقت
    professionalism,                 -- تقييم الاحترافية
    is_anonymous,                    -- هل التقييم مجهول؟
    created_at                       -- تاريخ التقييم
) VALUES (
    UUID(),                          -- معرف فريد للتقييم
    'request-uuid-here',             -- معرف الطلب
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'), -- العميل المقيم
    (SELECT service_provider_id FROM service_requests WHERE id = 'request-uuid-here'), -- مقدم الخدمة المقيم
    5,                               -- تقييم ممتاز (5 نجوم)
    'خدمة ممتازة وسريعة. الكهربائي محترف ولديه خبرة جيدة. حل المشكلة بسرعة ونظف المكان بعد الانتهاء. أنصح بالتعامل معه.', -- مراجعة إيجابية
    5,                               -- جودة خدمة ممتازة
    5,                               -- تواصل ممتاز
    5,                               -- التزام بالوقت ممتاز
    5,                               -- احترافية ممتازة
    FALSE,                           -- تقييم غير مجهول
    NOW()                            -- الوقت الحالي
);

-- تحديث إحصائيات مقدم الخدمة تلقائياً
-- استدعاء الإجراء المخزن لإعادة حساب التقييم
CALL UpdateUserRating(
    (SELECT service_provider_id FROM service_requests WHERE id = 'request-uuid-here')
);
```

---

## 💬 **إدارة المحادثات**

### 📱 **مثال 7: إرسال رسالة**

```sql
-- إرسال رسالة في محادثة
-- الغرض: مقدم الخدمة يرسل رسالة للعميل

INSERT INTO messages (
    id,                              -- معرف الرسالة
    conversation_id,                 -- معرف المحادثة
    sender_id,                       -- معرف المرسل
    message_type,                    -- نوع الرسالة
    content,                         -- محتوى الرسالة
    created_at                       -- تاريخ الإرسال
) VALUES (
    UUID(),                          -- معرف فريد للرسالة
    'conversation-uuid-here',        -- معرف المحادثة
    'provider-uuid-here',            -- مقدم الخدمة المرسل
    'text',                          -- رسالة نصية
    'مرحباً، أنا في الطريق إليك. سأصل خلال 15 دقيقة تقريباً. الرجاء التأكد من توفر الكهرباء في المنطقة المجاورة للفحص.', -- محتوى الرسالة
    NOW()                            -- الوقت الحالي
);

-- تحديث وقت آخر رسالة في المحادثة
UPDATE conversations 
SET 
    last_message_at = NOW(),         -- تحديث وقت آخر رسالة
    updated_at = NOW()               -- تحديث طابع التعديل
WHERE id = 'conversation-uuid-here'; -- معرف المحادثة

-- إنشاء إشعار للمستقبل
INSERT INTO notifications (
    id, user_id, title, message, notification_type, related_id, related_type, created_at
) VALUES (
    UUID(),
    (SELECT client_id FROM service_requests WHERE id = 'request-uuid-here'), -- العميل المستقبل
    'رسالة جديدة',
    'لديك رسالة جديدة من مقدم الخدمة',
    'new_message',
    'conversation-uuid-here',
    'message',
    NOW()
);
```

---

## 📊 **التقارير والإحصائيات**

### 📈 **مثال 8: تقرير أداء شهري**

```sql
-- تقرير أداء شهري شامل
-- الغرض: إنتاج تقرير شامل عن أداء التطبيق خلال الشهر الماضي

SELECT 
    -- إحصائيات المستخدمين
    (SELECT COUNT(*) FROM users WHERE role = 'client' 
     AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS new_clients_this_month,
    
    (SELECT COUNT(*) FROM users WHERE role = 'service_provider' 
     AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS new_providers_this_month,
    
    -- إحصائيات الطلبات
    (SELECT COUNT(*) FROM service_requests 
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS total_requests_this_month,
    
    (SELECT COUNT(*) FROM service_requests 
     WHERE status = 'completed' 
     AND completed_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS completed_requests_this_month,
    
    -- إحصائيات مالية
    (SELECT COALESCE(SUM(amount), 0) FROM payments 
     WHERE status = 'completed' 
     AND paid_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS total_revenue_this_month,
    
    (SELECT COALESCE(SUM(platform_commission), 0) FROM payments 
     WHERE status = 'completed' 
     AND paid_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS platform_earnings_this_month,
    
    -- معدلات الأداء
    ROUND(
        (SELECT COUNT(*) FROM service_requests 
         WHERE status = 'completed' 
         AND completed_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) * 100.0 /
        NULLIF((SELECT COUNT(*) FROM service_requests 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)), 0), 2
    ) AS completion_rate_this_month,
    
    -- متوسط التقييم
    (SELECT ROUND(AVG(rating), 2) FROM ratings 
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) AS avg_rating_this_month;
```

---

## 🎉 **الخلاصة**

هذه الأمثلة العملية توضح:

### ✨ **الميزات الرئيسية:**
- **تعليقات شاملة**: شرح كل خطوة بالتفصيل
- **أمثلة واقعية**: حالات استخدام حقيقية
- **أفضل الممارسات**: استخدام صحيح للاستعلامات
- **إدارة الأخطاء**: التحقق من الشروط قبل التنفيذ

### 🚀 **الاستخدام الأمثل:**
- **للمطورين**: فهم كيفية التعامل مع قاعدة البيانات
- **للتدريب**: تعلم أفضل الممارسات
- **للصيانة**: مرجع سريع للعمليات الشائعة
- **للتطوير**: قوالب جاهزة للاستخدام

**🎯 أمثلة عملية شاملة مع تعليقات مفصلة لسهولة الفهم والتطبيق! ✨**
