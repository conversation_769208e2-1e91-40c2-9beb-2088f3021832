# تطبيق مشوارك معنا

تطبيق خدمات متعدد الأغراض مصمم خصيصاً لمحافظة إب، اليمن. يوفر التطبيق منصة موحدة للعملاء ومقدمي الخدمات للتواصل وتبادل الخدمات المختلفة.

## 🌟 الميزات الرئيسية

### للعملاء:
- **طلب خدمات متنوعة**: سائق، كهربائي، سباك، نجار، ميكانيكي، عامل نظافة، دهان، بستاني
- **واجهة سهلة الاستخدام**: تصميم بديهي ومريح للعين
- **تتبع الطلبات**: متابعة حالة الطلب في الوقت الفعلي
- **محادثة مباشرة**: التواصل مع مقدم الخدمة
- **شفافية الأسعار**: عرض الأسعار المتوقعة والنهائية
- **خيارات دفع متعددة**: نقدي وإلكتروني (قريباً)

### لمقدمي الخدمات:
- **إدارة الطلبات**: قبول أو رفض الطلبات الواردة
- **تحديث الحالة**: متاح/غير متاح للعمل
- **تتبع الأرباح**: عرض الأرباح اليومية والشهرية
- **تقييمات العملاء**: بناء سمعة مهنية
- **إشعارات فورية**: تنبيهات للطلبات الجديدة

## 🎨 التصميم والواجهات

### الشاشات المتوفرة:
1. **شاشة البداية (Splash Screen)**: عرض شعار التطبيق مع تحميل أنيق
2. **اختيار الدور**: اختيار بين عميل أو مقدم خدمة
3. **تسجيل الدخول الديناميكي**: يتكيف مع نوع المستخدم المختار
4. **التسجيل الديناميكي**: حقول إضافية حسب نوع الخدمة
5. **الشاشة الرئيسية للعميل**: عرض الخدمات والطلبات النشطة
6. **الشاشة الرئيسية لمقدم الخدمة**: إدارة الطلبات والأرباح
7. **شاشة المحادثة**: تواصل مباشر بين الأطراف
8. **تفاصيل الطلب**: عرض شامل لمعلومات الطلب
9. **الإشعارات**: إدارة التنبيهات والرسائل
10. **إنشاء طلب جديد**: واجهة سهلة لطلب الخدمات

### الألوان والتصميم:
- **الألوان الأساسية**: أخضر (#2E7D32) وأزرق (#1976D2)
- **خط عربي**: Cairo من Google Fonts
- **تصميم متجاوب**: يتكيف مع أحجام الشاشات المختلفة
- **أيقونات واضحة**: رموز معبرة لكل نوع خدمة

## 🛠 التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Provider**: إدارة الحالة
- **Google Fonts**: الخطوط العربية
- **Flutter ScreenUtil**: التصميم المتجاوب
- **Google Maps**: (للمستقبل) عرض المواقع
- **Geolocator**: تحديد الموقع

## 📱 متطلبات النظام

- **Flutter SDK**: 3.6.1 أو أحدث
- **Dart SDK**: 3.0.0 أو أحدث
- **Android**: API level 21 أو أحدث
- **iOS**: iOS 12.0 أو أحدث

## 🚀 تشغيل التطبيق

### 1. تحضير البيئة:
```bash
# تأكد من تثبيت Flutter
flutter doctor

# استنساخ المشروع
git clone [repository-url]
cd mishwarak_maana
```

### 2. تثبيت التبعيات:
```bash
flutter pub get
```

### 3. تشغيل التطبيق:
```bash
# للأندرويد
flutter run

# للـ iOS
flutter run -d ios

# للويندوز
flutter run -d windows
```

## 📂 هيكل المشروع

```
lib/
├── constants/          # الثوابت والألوان
│   ├── app_colors.dart
│   └── app_strings.dart
├── models/            # نماذج البيانات
│   ├── user_model.dart
│   └── service_request_model.dart
├── providers/         # إدارة الحالة
│   └── app_state_provider.dart
├── screens/           # الشاشات
│   ├── auth/         # شاشات المصادقة
│   ├── home/         # الشاشات الرئيسية
│   ├── chat/         # شاشة المحادثة
│   ├── request/      # شاشات الطلبات
│   └── notifications/ # شاشة الإشعارات
├── widgets/          # المكونات المخصصة
│   ├── custom_button.dart
│   ├── custom_text_field.dart
│   └── service_type_dropdown.dart
└── main.dart         # نقطة البداية
```

## 🌍 التحديات البيئية المراعاة

### ضعف الاتصال:
- تصميم يقلل من استهلاك البيانات
- حفظ البيانات محلياً
- مؤشرات تحميل واضحة

### انقطاع الكهرباء:
- واجهات سريعة التحميل
- تحسين استهلاك البطارية
- حفظ الحالة عند إغلاق التطبيق

### خيارات الدفع:
- التركيز على الدفع النقدي
- إعداد للدفع الإلكتروني مستقبلاً

## 🔮 الميزات المستقبلية

- **خرائط تفاعلية**: عرض المواقع على الخريطة
- **تتبع الموقع المباشر**: متابعة موقع مقدم الخدمة
- **نظام التقييمات**: تقييم الخدمات والمقدمين
- **الدفع الإلكتروني**: دعم المحافظ الرقمية
- **إشعارات فورية**: Push notifications
- **دعم متعدد اللغات**: إضافة الإنجليزية
- **وضع الليل**: Dark mode
- **تحليلات الاستخدام**: إحصائيات للمقدمين

## 🤝 المساهمة

نرحب بالمساهمات لتطوير التطبيق:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: فريق تطوير مشوارك معنا
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.mishwarak-maana.com

## 🙏 شكر وتقدير

- **مجتمع Flutter**: للدعم والموارد
- **Google Fonts**: للخطوط العربية الجميلة
- **مجتمع إب**: للإلهام والدعم المحلي

---

**مشوارك معنا - خدماتك في متناول يدك** 🚗✨
