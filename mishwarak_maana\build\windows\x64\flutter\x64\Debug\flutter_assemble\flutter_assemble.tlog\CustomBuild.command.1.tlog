^D:\LAB\MISHWARAK_MAANA\BUILD\WINDOWS\X64\CMAKEFILES\B618E6B205E5D7DE65BCD060A25CC05E\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\development\flutter PROJECT_DIR=D:\lab\mishwarak_maana FLUTTER_ROOT=D:\development\flutter FLUTTER_EPHEMERAL_DIR=D:\lab\mishwarak_maana\windows\flutter\ephemeral PROJECT_DIR=D:\lab\mishwarak_maana FLUTTER_TARGET=D:\lab\mishwarak_maana\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\lab\mishwarak_maana\.dart_tool\package_config.json D:/development/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\LAB\MISHWARAK_MAANA\BUILD\WINDOWS\X64\CMAKEFILES\332B1AF89B7FECD25920CDF58B78146E\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\LAB\MISHWARAK_MAANA\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/lab/mishwarak_maana/windows -BD:/lab/mishwarak_maana/build/windows/x64 --check-stamp-file D:/lab/mishwarak_maana/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
