-- =====================================================
-- 🔍 استعلامات مفيدة لتطبيق خدمتي بلاس
-- =====================================================
-- تاريخ الإنشاء: 2024
-- الإصدار: 1.0
-- الوصف: مجموعة شاملة من الاستعلامات المفيدة للتطبيق
-- المطور: فريق خدمتي بلاس
-- الاستخدام: نسخ الاستعلام المطلوب وتعديل المعاملات حسب الحاجة
-- =====================================================

-- =====================================================
-- 📊 قسم استعلامات التقارير والإحصائيات
-- الغرض: استعلامات لإنتاج التقارير الإدارية والإحصائيات العامة
-- =====================================================

-- 1. إحصائيات عامة للتطبيق
-- الغرض: عرض نظرة عامة سريعة على أداء التطبيق
-- الاستخدام: لوحة التحكم الإدارية والتقارير اليومية
SELECT
    (SELECT COUNT(*) FROM users WHERE role = 'client') as total_clients,           -- إجمالي عدد العملاء
    (SELECT COUNT(*) FROM users WHERE role = 'service_provider') as total_providers, -- إجمالي عدد مقدمي الخدمات
    (SELECT COUNT(*) FROM service_requests) as total_requests,                     -- إجمالي عدد الطلبات
    (SELECT COUNT(*) FROM service_requests WHERE status = 'completed') as completed_requests, -- الطلبات المكتملة
    (SELECT SUM(amount) FROM payments WHERE status = 'completed') as total_revenue; -- إجمالي الإيرادات

-- 2. أفضل مقدمي الخدمات
-- الغرض: ترتيب مقدمي الخدمات حسب الأداء والتقييم
-- الاستخدام: صفحة "أفضل مقدمي الخدمات" وتقارير الجودة
-- المعايير: التقييم، معدل الإكمال، عدد الخدمات المنجزة
SELECT
    u.full_name,                                                                    -- اسم مقدم الخدمة
    u.service_type,                                                                 -- نوع الخدمة المقدمة
    u.rating,                                                                       -- متوسط التقييم
    u.total_ratings,                                                                -- عدد التقييمات الإجمالي
    COUNT(sr.id) as total_services,                                                 -- إجمالي الخدمات المطلوبة
    COUNT(CASE WHEN sr.status = 'completed' THEN 1 END) as completed_services,     -- الخدمات المكتملة
    ROUND(COUNT(CASE WHEN sr.status = 'completed' THEN 1 END) * 100.0 / COUNT(sr.id), 2) as completion_rate -- معدل الإكمال بالنسبة المئوية
FROM users u
LEFT JOIN service_requests sr ON u.id = sr.service_provider_id                     -- ربط مع طلبات الخدمة
WHERE u.role = 'service_provider'                                                  -- مقدمي الخدمات فقط
  AND u.is_active = TRUE                                                           -- النشطين فقط
GROUP BY u.id                                                                      -- تجميع حسب المستخدم
HAVING COUNT(sr.id) > 0                                                            -- الذين لديهم طلبات فقط
ORDER BY u.rating DESC, completion_rate DESC                                       -- ترتيب حسب التقييم ومعدل الإكمال
LIMIT 20;                                                                          -- أفضل 20 مقدم خدمة

-- 3. إحصائيات الخدمات الشهرية
SELECT 
    s.name as service_name,
    DATE_FORMAT(sr.created_at, '%Y-%m') as month,
    COUNT(*) as request_count,
    COUNT(CASE WHEN sr.status = 'completed' THEN 1 END) as completed_count,
    AVG(sr.final_price) as avg_price,
    SUM(CASE WHEN sr.status = 'completed' THEN sr.final_price ELSE 0 END) as total_revenue
FROM service_requests sr
JOIN services s ON sr.service_id = s.id
WHERE sr.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
GROUP BY s.id, DATE_FORMAT(sr.created_at, '%Y-%m')
ORDER BY month DESC, request_count DESC;

-- 4. تقرير الأرباح اليومية
SELECT 
    DATE(p.paid_at) as payment_date,
    COUNT(*) as payment_count,
    SUM(p.amount) as total_amount,
    SUM(p.platform_commission) as platform_earnings,
    SUM(p.net_amount) as provider_earnings
FROM payments p
WHERE p.status = 'completed' 
  AND p.paid_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(p.paid_at)
ORDER BY payment_date DESC;

-- =====================================================
-- 🔎 استعلامات البحث والفلترة
-- =====================================================

-- 5. البحث عن مقدمي الخدمات المتاحين
SELECT 
    u.id,
    u.full_name,
    u.phone,
    u.service_type,
    u.rating,
    u.total_ratings,
    up.city,
    up.district,
    up.bio,
    up.experience_years
FROM users u
JOIN user_profiles up ON u.id = up.user_id
WHERE u.role = 'service_provider'
  AND u.is_active = TRUE
  AND u.service_type = 'electrician'  -- يمكن تغيير نوع الخدمة
  AND up.city = 'إب'  -- يمكن تغيير المدينة
ORDER BY u.rating DESC, u.total_ratings DESC;

-- 6. البحث في الطلبات حسب المعايير
SELECT 
    sr.*,
    c.full_name as client_name,
    c.phone as client_phone,
    sp.full_name as provider_name,
    s.name as service_name,
    l.address as service_address
FROM service_requests sr
JOIN users c ON sr.client_id = c.id
LEFT JOIN users sp ON sr.service_provider_id = sp.id
JOIN services s ON sr.service_id = s.id
LEFT JOIN locations l ON sr.id = l.request_id AND l.location_type = 'service_location'
WHERE sr.status = 'pending'  -- يمكن تغيير الحالة
  AND sr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY sr.created_at DESC;

-- 7. البحث في المحادثات والرسائل
SELECT 
    c.id as conversation_id,
    c.title,
    u1.full_name as participant_1,
    u2.full_name as participant_2,
    c.last_message_at,
    (SELECT COUNT(*) FROM messages WHERE conversation_id = c.id AND is_read = FALSE) as unread_count
FROM conversations c
JOIN users u1 ON c.participant_1_id = u1.id
JOIN users u2 ON c.participant_2_id = u2.id
WHERE (c.participant_1_id = 'USER_ID_HERE' OR c.participant_2_id = 'USER_ID_HERE')
  AND c.is_active = TRUE
ORDER BY c.last_message_at DESC;

-- =====================================================
-- 📱 استعلامات التطبيق الأساسية
-- =====================================================

-- 8. تسجيل الدخول والتحقق من المستخدم
-- الغرض: التحقق من بيانات المستخدم عند تسجيل الدخول
-- الاستخدام: API تسجيل الدخول، التحقق من صحة البيانات
-- ملاحظة: يجب استبدال البريد الإلكتروني بالقيمة الفعلية
SELECT
    u.id,                                                                          -- معرف المستخدم للجلسة
    u.full_name,                                                                   -- الاسم الكامل للعرض
    u.email,                                                                       -- البريد الإلكتروني
    u.phone,                                                                       -- رقم الهاتف
    u.role,                                                                        -- دور المستخدم (عميل/مقدم خدمة)
    u.service_type,                                                                -- نوع الخدمة (للمقدمين)
    u.profile_image,                                                               -- صورة الملف الشخصي
    u.is_active,                                                                   -- حالة نشاط الحساب
    u.is_verified,                                                                 -- حالة التحقق
    u.rating,                                                                      -- تقييم المستخدم
    up.city,                                                                       -- المدينة من الملف الشخصي
    up.district                                                                    -- الحي من الملف الشخصي
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id                                   -- ربط مع الملف الشخصي
WHERE u.email = '<EMAIL>'                                                -- البريد الإلكتروني المدخل
  AND u.is_active = TRUE;                                                         -- الحسابات النشطة فقط

-- 9. الحصول على الطلبات النشطة للعميل
SELECT 
    sr.*,
    s.name as service_name,
    s.icon as service_icon,
    sp.full_name as provider_name,
    sp.phone as provider_phone,
    sp.rating as provider_rating,
    l.address as service_address
FROM service_requests sr
JOIN services s ON sr.service_id = s.id
LEFT JOIN users sp ON sr.service_provider_id = sp.id
LEFT JOIN locations l ON sr.id = l.request_id AND l.location_type = 'service_location'
WHERE sr.client_id = 'CLIENT_ID_HERE'
  AND sr.status IN ('pending', 'accepted', 'in_progress')
ORDER BY sr.created_at DESC;

-- 10. الحصول على الطلبات المتاحة لمقدم الخدمة
SELECT 
    sr.*,
    s.name as service_name,
    c.full_name as client_name,
    c.phone as client_phone,
    c.rating as client_rating,
    l.address as service_address,
    l.latitude,
    l.longitude
FROM service_requests sr
JOIN services s ON sr.service_id = s.id
JOIN users c ON sr.client_id = c.id
LEFT JOIN locations l ON sr.id = l.request_id AND l.location_type = 'service_location'
WHERE sr.status = 'pending'
  AND s.service_type = 'electrician'  -- نوع الخدمة لمقدم الخدمة
  AND sr.service_provider_id IS NULL
ORDER BY sr.created_at DESC;

-- 11. الحصول على الإشعارات غير المقروءة
SELECT 
    n.*,
    CASE 
        WHEN n.related_type = 'request' THEN (
            SELECT CONCAT('طلب: ', title) FROM service_requests WHERE id = n.related_id
        )
        WHEN n.related_type = 'user' THEN (
            SELECT full_name FROM users WHERE id = n.related_id
        )
        ELSE 'غير محدد'
    END as related_info
FROM notifications n
WHERE n.user_id = 'USER_ID_HERE'
  AND n.is_read = FALSE
ORDER BY n.created_at DESC
LIMIT 20;

-- 12. الحصول على تاريخ المحادثة
SELECT 
    m.*,
    u.full_name as sender_name,
    u.profile_image as sender_image
FROM messages m
JOIN users u ON m.sender_id = u.id
WHERE m.conversation_id = 'CONVERSATION_ID_HERE'
ORDER BY m.created_at ASC;

-- =====================================================
-- 📊 استعلامات الإحصائيات الشخصية
-- =====================================================

-- 13. إحصائيات مقدم الخدمة
SELECT 
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
    ROUND(AVG(CASE WHEN status = 'completed' THEN final_price END), 2) as avg_service_price,
    SUM(CASE WHEN status = 'completed' THEN final_price ELSE 0 END) as total_earnings
FROM service_requests
WHERE service_provider_id = 'PROVIDER_ID_HERE';

-- 14. إحصائيات العميل
SELECT 
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
    ROUND(AVG(CASE WHEN status = 'completed' THEN final_price END), 2) as avg_spent,
    SUM(CASE WHEN status = 'completed' THEN final_price ELSE 0 END) as total_spent
FROM service_requests
WHERE client_id = 'CLIENT_ID_HERE';

-- 15. أحدث التقييمات المستلمة
SELECT 
    r.*,
    u.full_name as rater_name,
    sr.title as service_title,
    s.name as service_name
FROM ratings r
JOIN users u ON r.rater_id = u.id
JOIN service_requests sr ON r.request_id = sr.id
JOIN services s ON sr.service_id = s.id
WHERE r.rated_id = 'USER_ID_HERE'
ORDER BY r.created_at DESC
LIMIT 10;

-- =====================================================
-- 🔧 استعلامات الصيانة والإدارة
-- =====================================================

-- 16. تنظيف الرموز المنتهية الصلاحية
DELETE FROM auth_tokens 
WHERE expires_at < NOW() OR is_revoked = TRUE;

-- 17. تنظيف الإشعارات القديمة
DELETE FROM notifications 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY) 
  AND is_read = TRUE;

-- 18. تحديث إحصائيات المستخدمين
UPDATE users u
SET 
    rating = (
        SELECT COALESCE(AVG(rating), 0) 
        FROM ratings 
        WHERE rated_id = u.id
    ),
    total_ratings = (
        SELECT COUNT(*) 
        FROM ratings 
        WHERE rated_id = u.id
    )
WHERE u.role = 'service_provider';

-- 19. البحث عن الطلبات المعلقة لفترة طويلة
SELECT 
    sr.*,
    c.full_name as client_name,
    s.name as service_name,
    TIMESTAMPDIFF(HOUR, sr.created_at, NOW()) as hours_pending
FROM service_requests sr
JOIN users c ON sr.client_id = c.id
JOIN services s ON sr.service_id = s.id
WHERE sr.status = 'pending'
  AND sr.created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY sr.created_at ASC;

-- 20. تقرير المستخدمين غير النشطين
SELECT 
    u.*,
    up.city,
    TIMESTAMPDIFF(DAY, u.last_login, NOW()) as days_inactive
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY)
  OR u.last_login IS NULL
ORDER BY u.last_login ASC;

-- =====================================================
-- تم إنشاء مجموعة شاملة من الاستعلامات المفيدة! 🎉
-- =====================================================
