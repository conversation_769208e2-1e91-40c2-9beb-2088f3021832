-- =====================================================
-- 🗄️ سكريبت إنشاء قاعدة بيانات خدمتي بلاس
-- =====================================================
-- تاريخ الإنشاء: 2024
-- الإصدار: 1.0
-- الوصف: سكريبت شامل لإنشاء جميع جداول قاعدة البيانات
-- المطور: فريق خدمتي بلاس
-- الترخيص: خاص بالمشروع
-- =====================================================

-- إنشاء قاعدة البيانات الرئيسية
-- استخدام utf8mb4 لدعم الأحرف العربية والرموز التعبيرية
CREATE DATABASE IF NOT EXISTS khedmaty_plus
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- التبديل إلى قاعدة البيانات المنشأة
USE khedmaty_plus;

-- =====================================================
-- 1. جدول المستخدمين (users)
-- الغرض: تخزين بيانات جميع مستخدمي التطبيق (عملاء ومقدمي خدمات)
-- =====================================================
CREATE TABLE users (
    -- المعرف الفريد للمستخدم (UUID لضمان الفرادة)
    id VARCHAR(36) PRIMARY KEY,

    -- البيانات الشخصية الأساسية
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل للمستخدم',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT 'البريد الإلكتروني (فريد)',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الهاتف (فريد)',
    password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',

    -- تصنيف المستخدم ونوع الخدمة
    role ENUM('client', 'service_provider') NOT NULL COMMENT 'دور المستخدم: عميل أو مقدم خدمة',
    service_type ENUM('driver', 'electrician', 'plumber', 'carpenter',
                     'mechanic', 'cleaner', 'painter', 'gardener') NULL
                     COMMENT 'نوع الخدمة (للمقدمين فقط)',

    -- الملف الشخصي والحالة
    profile_image VARCHAR(255) NULL COMMENT 'رابط صورة الملف الشخصي',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الحساب',
    is_verified BOOLEAN DEFAULT FALSE COMMENT 'حالة التحقق من الحساب',

    -- نظام التقييم
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT 'متوسط التقييم (0.00-5.00)',
    total_ratings INT DEFAULT 0 COMMENT 'إجمالي عدد التقييمات',

    -- طوابع زمنية للتتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الحساب',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    last_login TIMESTAMP NULL COMMENT 'تاريخ آخر تسجيل دخول',

    -- الفهارس لتحسين الأداء
    INDEX idx_email (email) COMMENT 'فهرس البريد الإلكتروني',
    INDEX idx_phone (phone) COMMENT 'فهرس رقم الهاتف',
    INDEX idx_role (role) COMMENT 'فهرس دور المستخدم',
    INDEX idx_service_type (service_type) COMMENT 'فهرس نوع الخدمة',
    INDEX idx_rating (rating) COMMENT 'فهرس التقييم',
    INDEX idx_created_at (created_at) COMMENT 'فهرس تاريخ الإنشاء'
);

-- =====================================================
-- 2. جدول ملفات المستخدمين (user_profiles)
-- الغرض: تخزين المعلومات التفصيلية والشخصية للمستخدمين
-- =====================================================
CREATE TABLE user_profiles (
    -- المعرف الفريد للملف الشخصي
    id VARCHAR(36) PRIMARY KEY,

    -- ربط بجدول المستخدمين (علاقة واحد لواحد)
    user_id VARCHAR(36) NOT NULL COMMENT 'معرف المستخدم المرتبط',

    -- البيانات الشخصية التفصيلية
    date_of_birth DATE NULL COMMENT 'تاريخ الميلاد',
    gender ENUM('male', 'female') NULL COMMENT 'الجنس: ذكر أو أنثى',
    national_id VARCHAR(50) NULL COMMENT 'رقم الهوية الوطنية',

    -- معلومات العنوان والموقع
    address TEXT NULL COMMENT 'العنوان التفصيلي',
    city VARCHAR(50) NULL COMMENT 'المدينة',
    district VARCHAR(50) NULL COMMENT 'الحي أو المنطقة',

    -- معلومات مهنية (خاصة بمقدمي الخدمات)
    bio TEXT NULL COMMENT 'نبذة شخصية أو مهنية',
    experience_years INT NULL COMMENT 'سنوات الخبرة في المجال',

    -- بيانات منظمة بصيغة JSON
    certifications JSON NULL COMMENT 'الشهادات والمؤهلات (JSON)',
    languages JSON NULL COMMENT 'اللغات المتحدث بها (JSON)',
    availability_hours JSON NULL COMMENT 'ساعات التوفر للعمل (JSON)',
    emergency_contact JSON NULL COMMENT 'جهة الاتصال في الطوارئ (JSON)',

    -- طوابع زمنية للتتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الملف',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',

    -- العلاقات والقيود
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE COMMENT 'حذف الملف عند حذف المستخدم',

    -- الفهارس لتحسين الأداء
    INDEX idx_user_id (user_id) COMMENT 'فهرس معرف المستخدم',
    INDEX idx_city (city) COMMENT 'فهرس المدينة للبحث الجغرافي',
    INDEX idx_district (district) COMMENT 'فهرس الحي للبحث المحلي'
);

-- =====================================================
-- 3. جدول رموز المصادقة (auth_tokens)
-- الغرض: إدارة رموز المصادقة والأمان للمستخدمين
-- =====================================================
CREATE TABLE auth_tokens (
    -- المعرف الفريد للرمز
    id VARCHAR(36) PRIMARY KEY,

    -- ربط بالمستخدم صاحب الرمز
    user_id VARCHAR(36) NOT NULL COMMENT 'معرف المستخدم صاحب الرمز',

    -- بيانات الرمز المشفر
    token_hash VARCHAR(255) NOT NULL COMMENT 'الرمز المشفر (Hash)',

    -- نوع الرمز وغرضه
    token_type ENUM('access', 'refresh', 'reset_password') NOT NULL
               COMMENT 'نوع الرمز: وصول، تجديد، أو إعادة تعيين كلمة المرور',

    -- إدارة صلاحية الرمز
    expires_at TIMESTAMP NOT NULL COMMENT 'تاريخ انتهاء صلاحية الرمز',
    is_revoked BOOLEAN DEFAULT FALSE COMMENT 'هل تم إلغاء الرمز؟',

    -- طابع زمني للإنشاء
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الرمز',

    -- العلاقات والقيود
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE COMMENT 'حذف الرموز عند حذف المستخدم',

    -- الفهارس لتحسين الأداء والأمان
    INDEX idx_user_id (user_id) COMMENT 'فهرس معرف المستخدم',
    INDEX idx_token_hash (token_hash) COMMENT 'فهرس الرمز المشفر للبحث السريع',
    INDEX idx_expires_at (expires_at) COMMENT 'فهرس تاريخ الانتهاء لتنظيف الرموز المنتهية'
);

-- =====================================================
-- 4. جدول الخدمات (services)
-- الغرض: تعريف أنواع الخدمات المتاحة في التطبيق وأسعارها
-- =====================================================
CREATE TABLE services (
    -- المعرف الفريد للخدمة
    id VARCHAR(36) PRIMARY KEY,

    -- تصنيف الخدمة
    service_type ENUM('driver', 'electrician', 'plumber', 'carpenter',
                     'mechanic', 'cleaner', 'painter', 'gardener') NOT NULL
                     COMMENT 'نوع الخدمة: سائق، كهربائي، سباك، نجار، ميكانيكي، منظف، دهان، بستاني',

    -- معلومات الخدمة الأساسية
    name VARCHAR(100) NOT NULL COMMENT 'اسم الخدمة باللغة العربية',
    description TEXT NULL COMMENT 'وصف تفصيلي للخدمة',
    icon VARCHAR(50) NULL COMMENT 'رمز أو أيقونة الخدمة',

    -- نظام التسعير المرن
    base_price DECIMAL(10,2) DEFAULT 0.00 COMMENT 'السعر الأساسي للخدمة',
    price_per_hour DECIMAL(10,2) NULL COMMENT 'السعر بالساعة (للخدمات الزمنية)',
    price_per_km DECIMAL(10,2) NULL COMMENT 'السعر بالكيلومتر (لخدمات النقل)',

    -- حالة الخدمة
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخدمة متاحة حالياً؟',

    -- طوابع زمنية للتتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إضافة الخدمة',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',

    -- الفهارس لتحسين الأداء
    INDEX idx_service_type (service_type) COMMENT 'فهرس نوع الخدمة للبحث السريع',
    INDEX idx_is_active (is_active) COMMENT 'فهرس حالة النشاط لعرض الخدمات المتاحة'
);

-- =====================================================
-- 5. جدول طلبات الخدمة (service_requests)
-- الغرض: الجدول المركزي لإدارة جميع طلبات الخدمات في التطبيق
-- =====================================================
CREATE TABLE service_requests (
    -- المعرف الفريد للطلب
    id VARCHAR(36) PRIMARY KEY,

    -- أطراف الطلب (العميل ومقدم الخدمة)
    client_id VARCHAR(36) NOT NULL COMMENT 'معرف العميل طالب الخدمة',
    service_provider_id VARCHAR(36) NULL COMMENT 'معرف مقدم الخدمة (فارغ حتى القبول)',
    service_id VARCHAR(36) NOT NULL COMMENT 'معرف نوع الخدمة المطلوبة',

    -- تفاصيل الطلب
    title VARCHAR(200) NOT NULL COMMENT 'عنوان الطلب',
    description TEXT NOT NULL COMMENT 'وصف تفصيلي للخدمة المطلوبة',

    -- إدارة حالة الطلب
    status ENUM('pending', 'accepted', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending'
           COMMENT 'حالة الطلب: معلق، مقبول، قيد التنفيذ، مكتمل، ملغي',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium'
             COMMENT 'أولوية الطلب: منخفضة، متوسطة، عالية، عاجلة',

    -- إدارة التسعير
    estimated_price DECIMAL(10,2) NULL COMMENT 'السعر المقدر للخدمة',
    final_price DECIMAL(10,2) NULL COMMENT 'السعر النهائي المتفق عليه',

    -- إدارة الدفع
    payment_method ENUM('cash', 'electronic') NOT NULL COMMENT 'طريقة الدفع: نقدي أو إلكتروني',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending'
                   COMMENT 'حالة الدفع: معلق، مدفوع، مسترد',

    -- الطوابع الزمنية لتتبع دورة حياة الطلب
    scheduled_at TIMESTAMP NULL COMMENT 'الموعد المجدول لتنفيذ الخدمة',
    accepted_at TIMESTAMP NULL COMMENT 'وقت قبول الطلب من مقدم الخدمة',
    started_at TIMESTAMP NULL COMMENT 'وقت بدء تنفيذ الخدمة',
    completed_at TIMESTAMP NULL COMMENT 'وقت إكمال الخدمة',
    cancelled_at TIMESTAMP NULL COMMENT 'وقت إلغاء الطلب',

    -- معلومات الإلغاء
    cancellation_reason TEXT NULL COMMENT 'سبب إلغاء الطلب (إن وجد)',

    -- معلومات إضافية مرنة
    additional_info JSON NULL COMMENT 'معلومات إضافية بصيغة JSON',

    -- طوابع زمنية أساسية
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الطلب',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',

    -- العلاقات والقيود
    FOREIGN KEY (client_id) REFERENCES users(id) ON DELETE CASCADE COMMENT 'حذف الطلبات عند حذف العميل',
    FOREIGN KEY (service_provider_id) REFERENCES users(id) ON DELETE SET NULL COMMENT 'إفراغ مقدم الخدمة عند حذفه',
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT COMMENT 'منع حذف الخدمة إذا كان لها طلبات',

    -- الفهارس لتحسين الأداء
    INDEX idx_client_id (client_id) COMMENT 'فهرس العميل لعرض طلباته',
    INDEX idx_service_provider_id (service_provider_id) COMMENT 'فهرس مقدم الخدمة لعرض طلباته',
    INDEX idx_service_id (service_id) COMMENT 'فهرس نوع الخدمة للإحصائيات',
    INDEX idx_status (status) COMMENT 'فهرس حالة الطلب للبحث والفلترة',
    INDEX idx_created_at (created_at) COMMENT 'فهرس تاريخ الإنشاء للترتيب الزمني',
    INDEX idx_scheduled_at (scheduled_at) COMMENT 'فهرس الموعد المجدول للتذكيرات'
);

-- =====================================================
-- 6. جدول المواقع (locations)
-- =====================================================
CREATE TABLE locations (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    location_type ENUM('pickup', 'dropoff', 'service_location') NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT NOT NULL,
    landmark VARCHAR(200) NULL,
    building_number VARCHAR(20) NULL,
    floor_number VARCHAR(10) NULL,
    apartment_number VARCHAR(10) NULL,
    special_instructions TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    INDEX idx_request_id (request_id),
    INDEX idx_location_type (location_type),
    INDEX idx_coordinates (latitude, longitude)
);

-- =====================================================
-- 7. جدول المدفوعات (payments)
-- =====================================================
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    payer_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    service_fee DECIMAL(10,2) DEFAULT 0.00,
    platform_commission DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'electronic', 'wallet') NOT NULL,
    payment_gateway VARCHAR(50) NULL,
    transaction_id VARCHAR(100) NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    refunded_at TIMESTAMP NULL,
    refund_reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (payer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_request_id (request_id),
    INDEX idx_payer_id (payer_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_status (status),
    INDEX idx_paid_at (paid_at)
);

-- =====================================================
-- 8. جدول التقييمات (ratings)
-- =====================================================
CREATE TABLE ratings (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    rater_id VARCHAR(36) NOT NULL,
    rated_id VARCHAR(36) NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT NULL,
    service_quality INT NULL CHECK (service_quality >= 1 AND service_quality <= 5),
    communication INT NULL CHECK (communication >= 1 AND communication <= 5),
    punctuality INT NULL CHECK (punctuality >= 1 AND punctuality <= 5),
    professionalism INT NULL CHECK (professionalism >= 1 AND professionalism <= 5),
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (rater_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rated_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_rating (request_id, rater_id, rated_id),
    INDEX idx_request_id (request_id),
    INDEX idx_rated_id (rated_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 9. جدول المحادثات (conversations)
-- =====================================================
CREATE TABLE conversations (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NULL,
    participant_1_id VARCHAR(36) NOT NULL,
    participant_2_id VARCHAR(36) NOT NULL,
    conversation_type ENUM('service_request', 'general', 'support') DEFAULT 'service_request',
    title VARCHAR(200) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE SET NULL,
    FOREIGN KEY (participant_1_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (participant_2_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_request_id (request_id),
    INDEX idx_participant_1 (participant_1_id),
    INDEX idx_participant_2 (participant_2_id),
    INDEX idx_last_message_at (last_message_at)
);

-- =====================================================
-- 10. جدول الرسائل (messages)
-- =====================================================
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message_type ENUM('text', 'image', 'file', 'location', 'system') DEFAULT 'text',
    content TEXT NOT NULL,
    file_url VARCHAR(500) NULL,
    file_name VARCHAR(255) NULL,
    file_size INT NULL,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    reply_to_id VARCHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_id) REFERENCES messages(id) ON DELETE SET NULL,
    
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
);

-- =====================================================
-- 11. جدول الإشعارات (notifications)
-- =====================================================
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type ENUM('new_request', 'request_accepted', 'request_rejected',
                          'service_completed', 'new_message', 'payment', 'general') NOT NULL,
    related_id VARCHAR(36) NULL,
    related_type ENUM('request', 'message', 'payment', 'user') NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 12. جدول رموز FCM (fcm_tokens)
-- =====================================================
CREATE TABLE fcm_tokens (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    token VARCHAR(500) NOT NULL,
    device_type ENUM('android', 'ios', 'web') NOT NULL,
    device_id VARCHAR(100) NULL,
    app_version VARCHAR(20) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active)
);

-- =====================================================
-- 13. جدول تتبع الموقع (location_tracking)
-- =====================================================
CREATE TABLE location_tracking (
    id VARCHAR(36) PRIMARY KEY,
    request_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(8, 2) NULL,
    speed DECIMAL(8, 2) NULL,
    heading DECIMAL(8, 2) NULL,
    altitude DECIMAL(8, 2) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_request_id (request_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_coordinates (latitude, longitude)
);

-- =====================================================
-- 14. جدول الإحصائيات (statistics)
-- =====================================================
CREATE TABLE statistics (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    stat_type ENUM('daily_earnings', 'monthly_earnings', 'total_requests',
                   'completed_requests', 'cancelled_requests', 'rating_average') NOT NULL,
    stat_date DATE NOT NULL,
    value DECIMAL(15, 2) NOT NULL,
    additional_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_stat (user_id, stat_type, stat_date),
    INDEX idx_user_id (user_id),
    INDEX idx_stat_type (stat_type),
    INDEX idx_stat_date (stat_date)
);

-- =====================================================
-- 15. جدول إعدادات النظام (system_settings)
-- =====================================================
CREATE TABLE system_settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- =====================================================
-- إدراج البيانات الأساسية
-- =====================================================

-- إدراج الخدمات الأساسية
INSERT INTO services (id, service_type, name, description, icon, base_price, price_per_hour, price_per_km, is_active) VALUES
(UUID(), 'driver', 'خدمة السائق', 'خدمة نقل الأشخاص والبضائع', '🚗', 10.00, 15.00, 2.00, TRUE),
(UUID(), 'electrician', 'خدمة الكهربائي', 'إصلاح وصيانة الأجهزة الكهربائية', '⚡', 20.00, 25.00, NULL, TRUE),
(UUID(), 'plumber', 'خدمة السباك', 'إصلاح وصيانة أنظمة السباكة', '🔧', 25.00, 30.00, NULL, TRUE),
(UUID(), 'carpenter', 'خدمة النجار', 'أعمال النجارة والأثاث', '🔨', 30.00, 35.00, NULL, TRUE),
(UUID(), 'mechanic', 'خدمة الميكانيكي', 'إصلاح وصيانة السيارات', '🔧', 35.00, 40.00, NULL, TRUE),
(UUID(), 'cleaner', 'خدمة التنظيف', 'تنظيف المنازل والمكاتب', '🧹', 15.00, 20.00, NULL, TRUE),
(UUID(), 'painter', 'خدمة الدهان', 'أعمال الطلاء والدهان', '🎨', 25.00, 30.00, NULL, TRUE),
(UUID(), 'gardener', 'خدمة البستاني', 'العناية بالحدائق والنباتات', '🌱', 20.00, 25.00, NULL, TRUE);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, is_public) VALUES
(UUID(), 'app_name', 'خدمتي بلاس', 'string', 'اسم التطبيق', TRUE),
(UUID(), 'app_version', '1.0.0', 'string', 'إصدار التطبيق', TRUE),
(UUID(), 'platform_commission', '10', 'number', 'عمولة المنصة بالنسبة المئوية', FALSE),
(UUID(), 'min_service_price', '5', 'number', 'أقل سعر للخدمة', TRUE),
(UUID(), 'max_service_price', '1000', 'number', 'أعلى سعر للخدمة', TRUE),
(UUID(), 'support_phone', '+************', 'string', 'رقم هاتف الدعم', TRUE),
(UUID(), 'support_email', '<EMAIL>', 'string', 'بريد الدعم الإلكتروني', TRUE),
(UUID(), 'business_hours', '{"start": "06:00", "end": "23:00"}', 'json', 'ساعات العمل', TRUE),
(UUID(), 'service_radius', '50', 'number', 'نطاق الخدمة بالكيلومتر', TRUE),
(UUID(), 'auto_assign_requests', 'false', 'boolean', 'تعيين الطلبات تلقائياً', FALSE);

-- =====================================================
-- إنشاء المشاهد (Views) المفيدة
-- =====================================================

-- مشهد إحصائيات المستخدمين
CREATE VIEW user_stats AS
SELECT
    u.id,
    u.full_name,
    u.role,
    u.service_type,
    u.rating,
    u.total_ratings,
    COUNT(DISTINCT sr.id) as total_requests,
    COUNT(DISTINCT CASE WHEN sr.status = 'completed' THEN sr.id END) as completed_requests,
    COUNT(DISTINCT CASE WHEN sr.status = 'cancelled' THEN sr.id END) as cancelled_requests,
    COALESCE(SUM(CASE WHEN p.status = 'completed' AND p.receiver_id = u.id THEN p.net_amount END), 0) as total_earnings
FROM users u
LEFT JOIN service_requests sr ON (u.id = sr.client_id OR u.id = sr.service_provider_id)
LEFT JOIN payments p ON sr.id = p.request_id
GROUP BY u.id;

-- مشهد الطلبات النشطة
CREATE VIEW active_requests AS
SELECT
    sr.*,
    c.full_name as client_name,
    c.phone as client_phone,
    sp.full_name as provider_name,
    sp.phone as provider_phone,
    s.name as service_name,
    s.icon as service_icon
FROM service_requests sr
JOIN users c ON sr.client_id = c.id
LEFT JOIN users sp ON sr.service_provider_id = sp.id
JOIN services s ON sr.service_id = s.id
WHERE sr.status IN ('pending', 'accepted', 'in_progress');

-- =====================================================
-- إنشاء الإجراءات المخزنة (Stored Procedures)
-- =====================================================

DELIMITER //

-- إجراء لحساب تقييم المستخدم
CREATE PROCEDURE UpdateUserRating(IN user_id VARCHAR(36))
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    DECLARE rating_count INT;

    SELECT AVG(rating), COUNT(*)
    INTO avg_rating, rating_count
    FROM ratings
    WHERE rated_id = user_id;

    UPDATE users
    SET rating = COALESCE(avg_rating, 0.00),
        total_ratings = COALESCE(rating_count, 0)
    WHERE id = user_id;
END //

-- إجراء لإنشاء إشعار
CREATE PROCEDURE CreateNotification(
    IN p_user_id VARCHAR(36),
    IN p_title VARCHAR(200),
    IN p_message TEXT,
    IN p_type VARCHAR(50),
    IN p_related_id VARCHAR(36),
    IN p_related_type VARCHAR(50),
    IN p_data JSON
)
BEGIN
    INSERT INTO notifications (
        id, user_id, title, message, notification_type,
        related_id, related_type, data, created_at
    ) VALUES (
        UUID(), p_user_id, p_title, p_message, p_type,
        p_related_id, p_related_type, p_data, NOW()
    );
END //

DELIMITER ;

-- =====================================================
-- إنشاء المؤشرات (Triggers)
-- =====================================================

DELIMITER //

-- مؤشر لتحديث التقييم عند إضافة تقييم جديد
CREATE TRIGGER after_rating_insert
AFTER INSERT ON ratings
FOR EACH ROW
BEGIN
    CALL UpdateUserRating(NEW.rated_id);
END //

-- مؤشر لتحديث آخر رسالة في المحادثة
CREATE TRIGGER after_message_insert
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    UPDATE conversations
    SET last_message_at = NEW.created_at
    WHERE id = NEW.conversation_id;
END //

DELIMITER ;

-- =====================================================
-- تم إنشاء قاعدة البيانات بنجاح! 🎉
-- =====================================================
