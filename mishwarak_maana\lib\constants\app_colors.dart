import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D32); // Green
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF1976D2); // Blue
  static const Color secondaryLight = Color(0xFF2196F3);
  static const Color secondaryDark = Color(0xFF0D47A1);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF9800); // Orange
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFE65100);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Service Type Colors
  static const Color driverColor = Color(0xFF2196F3);
  static const Color electricianColor = Color(0xFFFFEB3B);
  static const Color plumberColor = Color(0xFF00BCD4);
  static const Color carpenterColor = Color(0xFF8BC34A);
  static const Color mechanicColor = Color(0xFF9C27B0);
  static const Color cleanerColor = Color(0xFF00E676);
  static const Color painterColor = Color(0xFFE91E63);
  static const Color gardenerColor = Color(0xFF4CAF50);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  
  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);
  
  // Utility method to get service type color
  static Color getServiceTypeColor(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'driver':
        return driverColor;
      case 'electrician':
        return electricianColor;
      case 'plumber':
        return plumberColor;
      case 'carpenter':
        return carpenterColor;
      case 'mechanic':
        return mechanicColor;
      case 'cleaner':
        return cleanerColor;
      case 'painter':
        return painterColor;
      case 'gardener':
        return gardenerColor;
      default:
        return primary;
    }
  }
}
