class AppStrings {
  // App Info
  static const String appName = 'خدمتي بلاس';
  static const String appSlogan = 'جميع الخدمات في مكان واحد';

  // Authentication
  static const String login = 'تسجيل الدخول';
  static const String register = 'تسجيل جديد';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String fullName = 'الاسم الكامل';
  static const String phone = 'رقم الهاتف';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String dontHaveAccount = 'ليس لديك حساب؟ سجل الآن';
  static const String alreadyHaveAccount = 'لديك حساب بالفعل؟ سجل دخول';

  // Role Selection
  static const String selectRole = 'اختر دورك';
  static const String client = 'عميل';
  static const String serviceProvider = 'مقدم خدمة';
  static const String clientDescription = 'احصل على الخدمات التي تحتاجها';
  static const String serviceProviderDescription = 'قدم خدماتك واكسب المال';

  // Service Types
  static const String selectServiceType = 'اختر نوع الخدمة';
  static const String driver = 'سائق';
  static const String electrician = 'كهربائي';
  static const String plumber = 'سباك';
  static const String carpenter = 'نجار';
  static const String mechanic = 'ميكانيكي';
  static const String cleaner = 'عامل نظافة';
  static const String painter = 'دهان';
  static const String gardener = 'بستاني';

  // Home Screen
  static const String welcome = 'مرحباً';
  static const String requestService = 'اطلب خدمة';
  static const String requestRide = 'اطلب مشوار';
  static const String availableServices = 'الخدمات المتاحة';
  static const String nearbyProviders = 'مقدمو الخدمات القريبون';
  static const String activeRequests = 'الطلبات النشطة';
  static const String requestHistory = 'سجل الطلبات';

  // Service Provider Home
  static const String newRequests = 'طلبات جديدة';
  static const String acceptRequest = 'قبول الطلب';
  static const String declineRequest = 'رفض الطلب';
  static const String updateStatus = 'تحديث الحالة';
  static const String available = 'متاح';
  static const String unavailable = 'غير متاح';
  static const String earnings = 'الأرباح';

  // Request Details
  static const String requestDetails = 'تفاصيل الطلب';
  static const String serviceType = 'نوع الخدمة';
  static const String description = 'الوصف';
  static const String pickupLocation = 'موقع الانطلاق';
  static const String dropoffLocation = 'موقع الوصول';
  static const String estimatedPrice = 'السعر المتوقع';
  static const String finalPrice = 'السعر النهائي';
  static const String paymentMethod = 'طريقة الدفع';
  static const String payCash = 'دفع نقدي';
  static const String payElectronic = 'دفع إلكتروني';
  static const String cancelRequest = 'إلغاء الطلب';

  // Chat
  static const String chat = 'محادثة';
  static const String typeMessage = 'اكتب رسالة...';
  static const String send = 'إرسال';

  // Notifications
  static const String notifications = 'الإشعارات';
  static const String noNotifications = 'لا توجد إشعارات';
  static const String markAsRead = 'تحديد كمقروء';

  // Profile & Settings
  static const String profile = 'الملف الشخصي';
  static const String settings = 'الإعدادات';
  static const String editProfile = 'تعديل الملف الشخصي';
  static const String changePassword = 'تغيير كلمة المرور';
  static const String language = 'اللغة';
  static const String support = 'الدعم';
  static const String about = 'حول التطبيق';
  static const String logout = 'تسجيل الخروج';

  // Status Messages
  static const String loading = 'جاري التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String error = 'حدث خطأ';
  static const String success = 'تم بنجاح';
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String tryAgain = 'حاول مرة أخرى';

  // Buttons
  static const String ok = 'موافق';
  static const String cancel = 'إلغاء';
  static const String save = 'حفظ';
  static const String edit = 'تعديل';
  static const String delete = 'حذف';
  static const String confirm = 'تأكيد';
  static const String back = 'رجوع';
  static const String next = 'التالي';
  static const String skip = 'تخطي';
  static const String done = 'تم';

  // Validation Messages
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsNotMatch = 'كلمات المرور غير متطابقة';
  static const String invalidPhone = 'رقم الهاتف غير صحيح';

  // Additional Info Fields
  static const String vehiclePlateNumber = 'رقم لوحة المركبة';
  static const String vehicleModel = 'موديل السيارة';
  static const String specialization = 'التخصص الدقيق';
  static const String licenseNumber = 'رقم الرخصة/الاعتماد';
  static const String experience = 'سنوات الخبرة';

  // Map & Location
  static const String currentLocation = 'الموقع الحالي';
  static const String selectLocation = 'اختر الموقع';
  static const String searchLocation = 'البحث عن موقع';
  static const String locationPermissionRequired = 'إذن الموقع مطلوب';
  static const String enableLocation = 'تفعيل الموقع';
}
