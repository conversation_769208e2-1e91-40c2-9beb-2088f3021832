import 'package:flutter/material.dart';
import 'app_colors.dart';

/// نظام ألوان متقدم للأيقونات مع دعم الثيم الفاتح والداكن
class IconColors {
  // منع إنشاء كائن من الكلاس
  IconColors._();

  // ========== الألوان الأساسية للأيقونات ==========
  
  /// الأزرق الأساسي - للأيقونات المهمة
  static const Color primaryBlue = Color(0xFF2196F3);
  
  /// الأزرق الثانوي - للأيقونات الثانوية
  static const Color secondaryBlue = Color(0xFF03DAC6);
  
  /// الأخضر - للنجاح والإكمال
  static const Color successGreen = Color(0xFF4CAF50);
  
  /// الأحمر - للأخطاء والتحذيرات
  static const Color errorRed = Color(0xFFF44336);
  
  /// البرتقالي - للتنبيهات والانتظار
  static const Color warningOrange = Color(0xFFFF9800);
  
  /// البنفسجي - للميزات المميزة
  static const Color premiumPurple = Color(0xFF9C27B0);
  
  /// الذهبي - للتقييمات والجوائز
  static const Color goldYellow = Color(0xFFFFD700);

  // ========== ألوان الخدمات ==========
  
  /// أزرق النقل - للسيارات والمواصلات
  static const Color transportBlue = Color(0xFF1976D2);
  
  /// أصفر الكهرباء - للخدمات الكهربائية
  static const Color electricYellow = Color(0xFFFFC107);
  
  /// أزرق السباكة - لخدمات السباكة
  static const Color plumbingBlue = Color(0xFF00BCD4);
  
  /// بني النجارة - لأعمال النجارة
  static const Color carpentryBrown = Color(0xFF8D6E63);
  
  /// رمادي الميكانيكا - للإصلاحات الميكانيكية
  static const Color mechanicGray = Color(0xFF607D8B);
  
  /// أخضر التنظيف - لخدمات التنظيف
  static const Color cleaningGreen = Color(0xFF8BC34A);
  
  /// أحمر الطلاء - لأعمال الطلاء
  static const Color paintingRed = Color(0xFFE91E63);
  
  /// أخضر البستنة - لأعمال البستنة
  static const Color gardeningGreen = Color(0xFF4CAF50);

  // ========== ألوان الحالات ==========
  
  /// أخضر فاتح - للحالات النشطة
  static const Color activeGreen = Color(0xFF66BB6A);
  
  /// أحمر فاتح - للحالات غير النشطة
  static const Color inactiveRed = Color(0xFFEF5350);
  
  /// أصفر - للحالات المعلقة
  static const Color pendingYellow = Color(0xFFFFCA28);
  
  /// أزرق فاتح - للحالات قيد المعالجة
  static const Color processingBlue = Color(0xFF42A5F5);

  // ========== التدرجات اللونية ==========
  
  /// تدرج أزرق أساسي
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF2196F3),
      Color(0xFF21CBF3),
    ],
  );
  
  /// تدرج أخضر للنجاح
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF4CAF50),
      Color(0xFF8BC34A),
    ],
  );
  
  /// تدرج أحمر للأخطاء
  static const LinearGradient errorGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF44336),
      Color(0xFFE91E63),
    ],
  );
  
  /// تدرج برتقالي للتحذيرات
  static const LinearGradient warningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFF9800),
      Color(0xFFFFCA28),
    ],
  );

  // ========== دوال مساعدة ==========
  
  /// الحصول على لون الخدمة حسب النوع
  static Color getServiceColor(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'driver':
      case 'transport':
        return transportBlue;
      case 'electrician':
      case 'electric':
        return electricYellow;
      case 'plumber':
      case 'plumbing':
        return plumbingBlue;
      case 'carpenter':
      case 'carpentry':
        return carpentryBrown;
      case 'mechanic':
        return mechanicGray;
      case 'cleaner':
      case 'cleaning':
        return cleaningGreen;
      case 'painter':
      case 'painting':
        return paintingRed;
      case 'gardener':
      case 'gardening':
        return gardeningGreen;
      default:
        return primaryBlue;
    }
  }
  
  /// الحصول على لون الحالة
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'success':
        return activeGreen;
      case 'inactive':
      case 'cancelled':
      case 'failed':
        return inactiveRed;
      case 'pending':
      case 'waiting':
        return pendingYellow;
      case 'processing':
      case 'in_progress':
        return processingBlue;
      default:
        return AppColors.textSecondary;
    }
  }
  
  /// الحصول على لون مع شفافية
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  /// الحصول على لون فاتح من لون أساسي
  static Color getLightVariant(Color color) {
    return Color.lerp(color, Colors.white, 0.3) ?? color;
  }
  
  /// الحصول على لون داكن من لون أساسي
  static Color getDarkVariant(Color color) {
    return Color.lerp(color, Colors.black, 0.2) ?? color;
  }

  // ========== ألوان الثيم الداكن ==========
  
  /// ألوان الأيقونات للثيم الداكن
  static const Color darkPrimaryBlue = Color(0xFF64B5F6);
  static const Color darkSecondaryBlue = Color(0xFF4DD0E1);
  static const Color darkSuccessGreen = Color(0xFF81C784);
  static const Color darkErrorRed = Color(0xFFE57373);
  static const Color darkWarningOrange = Color(0xFFFFB74D);
  static const Color darkPremiumPurple = Color(0xFFBA68C8);
  static const Color darkGoldYellow = Color(0xFFFFD54F);

  // ========== دوال الثيم ==========
  
  /// الحصول على لون حسب الثيم
  static Color getThemedColor(Color lightColor, Color darkColor, bool isDark) {
    return isDark ? darkColor : lightColor;
  }
  
  /// الحصول على لون الأيقونة الأساسي حسب الثيم
  static Color getPrimaryIconColor(bool isDark) {
    return isDark ? darkPrimaryBlue : primaryBlue;
  }
  
  /// الحصول على لون الأيقونة الثانوي حسب الثيم
  static Color getSecondaryIconColor(bool isDark) {
    return isDark ? darkSecondaryBlue : secondaryBlue;
  }
  
  /// الحصول على لون النجاح حسب الثيم
  static Color getSuccessIconColor(bool isDark) {
    return isDark ? darkSuccessGreen : successGreen;
  }
  
  /// الحصول على لون الخطأ حسب الثيم
  static Color getErrorIconColor(bool isDark) {
    return isDark ? darkErrorRed : errorRed;
  }
  
  /// الحصول على لون التحذير حسب الثيم
  static Color getWarningIconColor(bool isDark) {
    return isDark ? darkWarningOrange : warningOrange;
  }

  // ========== ألوان خاصة للأيقونات ==========
  
  /// لون أيقونة الإشعارات
  static const Color notificationIcon = Color(0xFF2196F3);
  
  /// لون أيقونة الإعدادات
  static const Color settingsIcon = Color(0xFF607D8B);
  
  /// لون أيقونة الملف الشخصي
  static const Color profileIcon = Color(0xFF4CAF50);
  
  /// لون أيقونة الدعم
  static const Color supportIcon = Color(0xFFFF9800);
  
  /// لون أيقونة تسجيل الخروج
  static const Color logoutIcon = Color(0xFFF44336);
  
  /// لون أيقونة التاريخ
  static const Color historyIcon = Color(0xFF9C27B0);
  
  /// لون أيقونة الأرباح
  static const Color earningsIcon = Color(0xFFFFD700);
  
  /// لون أيقونة المعلومات
  static const Color infoIcon = Color(0xFF2196F3);

  // ========== مجموعات ألوان متناسقة ==========
  
  /// مجموعة ألوان الخدمات الأساسية
  static const List<Color> serviceColors = [
    transportBlue,
    electricYellow,
    plumbingBlue,
    carpentryBrown,
    mechanicGray,
    cleaningGreen,
    paintingRed,
    gardeningGreen,
  ];
  
  /// مجموعة ألوان الحالات
  static const List<Color> statusColors = [
    activeGreen,
    inactiveRed,
    pendingYellow,
    processingBlue,
  ];
  
  /// مجموعة ألوان التنقل
  static const List<Color> navigationColors = [
    notificationIcon,
    settingsIcon,
    profileIcon,
    supportIcon,
    historyIcon,
    infoIcon,
  ];
}
