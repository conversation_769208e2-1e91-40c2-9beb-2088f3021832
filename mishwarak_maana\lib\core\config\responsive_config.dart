import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Configuration class for responsive design
class ResponsiveConfig {
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1200;
  static const double desktopBreakpoint = 1920;

  // Screen size categories
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // Responsive font sizes
  static double getTitleFontSize(BuildContext context) {
    if (isMobile(context)) return 24.sp;
    if (isTablet(context)) return 28.sp;
    return 32.sp;
  }

  static double getHeadingFontSize(BuildContext context) {
    if (isMobile(context)) return 20.sp;
    if (isTablet(context)) return 24.sp;
    return 28.sp;
  }

  static double getBodyFontSize(BuildContext context) {
    if (isMobile(context)) return 14.sp;
    if (isTablet(context)) return 16.sp;
    return 18.sp;
  }

  static double getCaptionFontSize(BuildContext context) {
    if (isMobile(context)) return 12.sp;
    if (isTablet(context)) return 14.sp;
    return 16.sp;
  }

  // Responsive spacing
  static double getSmallSpacing(BuildContext context) {
    if (isMobile(context)) return 8.h;
    if (isTablet(context)) return 12.h;
    return 16.h;
  }

  static double getMediumSpacing(BuildContext context) {
    if (isMobile(context)) return 16.h;
    if (isTablet(context)) return 20.h;
    return 24.h;
  }

  static double getLargeSpacing(BuildContext context) {
    if (isMobile(context)) return 24.h;
    if (isTablet(context)) return 32.h;
    return 40.h;
  }

  static double getExtraLargeSpacing(BuildContext context) {
    if (isMobile(context)) return 32.h;
    if (isTablet(context)) return 48.h;
    return 64.h;
  }

  // Responsive padding
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h);
    }
    if (isTablet(context)) {
      return EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h);
    }
    return EdgeInsets.symmetric(horizontal: 48.w, vertical: 32.h);
  }

  static EdgeInsets getCardPadding(BuildContext context) {
    if (isMobile(context)) return EdgeInsets.all(16.w);
    if (isTablet(context)) return EdgeInsets.all(20.w);
    return EdgeInsets.all(24.w);
  }

  // Responsive border radius
  static double getSmallRadius(BuildContext context) {
    if (isMobile(context)) return 8.r;
    if (isTablet(context)) return 10.r;
    return 12.r;
  }

  static double getMediumRadius(BuildContext context) {
    if (isMobile(context)) return 12.r;
    if (isTablet(context)) return 16.r;
    return 20.r;
  }

  static double getLargeRadius(BuildContext context) {
    if (isMobile(context)) return 16.r;
    if (isTablet(context)) return 20.r;
    return 24.r;
  }

  // Responsive icon sizes
  static double getSmallIconSize(BuildContext context) {
    if (isMobile(context)) return 16.sp;
    if (isTablet(context)) return 20.sp;
    return 24.sp;
  }

  static double getMediumIconSize(BuildContext context) {
    if (isMobile(context)) return 24.sp;
    if (isTablet(context)) return 28.sp;
    return 32.sp;
  }

  static double getLargeIconSize(BuildContext context) {
    if (isMobile(context)) return 32.sp;
    if (isTablet(context)) return 40.sp;
    return 48.sp;
  }

  // Responsive button dimensions
  static double getButtonHeight(BuildContext context) {
    if (isMobile(context)) return 48.h;
    if (isTablet(context)) return 52.h;
    return 56.h;
  }

  static double getSmallButtonHeight(BuildContext context) {
    if (isMobile(context)) return 36.h;
    if (isTablet(context)) return 40.h;
    return 44.h;
  }

  // Grid columns for different screen sizes
  static int getGridColumns(BuildContext context) {
    if (isMobile(context)) {
      return isLandscape(context) ? 3 : 2;
    }
    if (isTablet(context)) {
      return isLandscape(context) ? 4 : 3;
    }
    return 5; // Desktop
  }

  // Maximum content width for large screens
  static double getMaxContentWidth(BuildContext context) {
    if (isDesktop(context)) return 1200.w;
    return double.infinity;
  }

  // Responsive card dimensions
  static double getCardMinHeight(BuildContext context) {
    if (isMobile(context)) return 120.h;
    if (isTablet(context)) return 140.h;
    return 160.h;
  }

  static double getCardMaxWidth(BuildContext context) {
    if (isMobile(context)) return double.infinity;
    if (isTablet(context)) return 400.w;
    return 500.w;
  }

  // Responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    if (isMobile(context)) return kToolbarHeight;
    if (isTablet(context)) return kToolbarHeight + 8;
    return kToolbarHeight + 16;
  }

  // Responsive bottom navigation height
  static double getBottomNavHeight(BuildContext context) {
    if (isMobile(context)) return 60.h;
    if (isTablet(context)) return 70.h;
    return 80.h;
  }

  // Responsive dialog width
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) return screenWidth * 0.9;
    if (isTablet(context)) return screenWidth * 0.7;
    return screenWidth * 0.5;
  }

  // Responsive list tile height
  static double getListTileHeight(BuildContext context) {
    if (isMobile(context)) return 72.h;
    if (isTablet(context)) return 80.h;
    return 88.h;
  }

  // Helper method to get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context) && desktop != null) return desktop;
    if (isTablet(context) && tablet != null) return tablet;
    return mobile;
  }

  // Responsive layout builder
  static Widget responsiveBuilder(
    BuildContext context, {
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint && desktop != null) {
          return desktop;
        } else if (constraints.maxWidth >= mobileBreakpoint && tablet != null) {
          return tablet;
        } else {
          return mobile;
        }
      },
    );
  }

  // Safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
    );
  }

  // Responsive image sizes
  static double getAvatarSize(BuildContext context) {
    if (isMobile(context)) return 40.w;
    if (isTablet(context)) return 48.w;
    return 56.w;
  }

  static double getLargeAvatarSize(BuildContext context) {
    if (isMobile(context)) return 80.w;
    if (isTablet(context)) return 100.w;
    return 120.w;
  }

  // Responsive elevation
  static double getCardElevation(BuildContext context) {
    if (isMobile(context)) return 2;
    if (isTablet(context)) return 4;
    return 6;
  }

  static double getButtonElevation(BuildContext context) {
    if (isMobile(context)) return 2;
    if (isTablet(context)) return 3;
    return 4;
  }
}
