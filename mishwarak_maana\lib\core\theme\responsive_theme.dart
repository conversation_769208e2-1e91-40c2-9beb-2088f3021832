import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_colors.dart';
import '../config/responsive_config.dart';

class ResponsiveTheme {
  static ThemeData getLightTheme(BuildContext context) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.surface,
        // ignore: deprecated_member_use
        background: AppColors.background,
        error: AppColors.error,
        onPrimary: AppColors.textOnPrimary,
        onSecondary: AppColors.textSecondary,
        onSurface: AppColors.textPrimary,
        // ignore: deprecated_member_use
        onBackground: AppColors.textPrimary,
        onError: AppColors.textOnPrimary,
      ),
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: ResponsiveConfig.getResponsiveValue(
          context,
          mobile: 2,
          tablet: 4,
          desktop: 6,
        ),
        centerTitle: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getHeadingFontSize(context),
          fontWeight: FontWeight.w600,
          color: AppColors.textOnPrimary,
        ),
        toolbarHeight: ResponsiveConfig.getAppBarHeight(context),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(ResponsiveConfig.getSmallRadius(context)),
            bottomRight: Radius.circular(ResponsiveConfig.getSmallRadius(context)),
          ),
        ),
      ),

      // Card Theme
      cardTheme: CardTheme(
        color: AppColors.surface,
        elevation: ResponsiveConfig.getCardElevation(context),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
        ),
        margin: EdgeInsets.symmetric(
          horizontal: ResponsiveConfig.getSmallSpacing(context),
          vertical: ResponsiveConfig.getSmallSpacing(context) / 2,
        ),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: ResponsiveConfig.getButtonElevation(context),
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveConfig.getMediumSpacing(context),
            vertical: ResponsiveConfig.getSmallSpacing(context),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          ),
          textStyle: GoogleFonts.cairo(
            fontSize: ResponsiveConfig.getBodyFontSize(context),
            fontWeight: FontWeight.w600,
          ),
          minimumSize: Size(
            double.infinity,
            ResponsiveConfig.getButtonHeight(context),
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: BorderSide(color: AppColors.primary, width: 2),
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveConfig.getMediumSpacing(context),
            vertical: ResponsiveConfig.getSmallSpacing(context),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          ),
          textStyle: GoogleFonts.cairo(
            fontSize: ResponsiveConfig.getBodyFontSize(context),
            fontWeight: FontWeight.w600,
          ),
          minimumSize: Size(
            double.infinity,
            ResponsiveConfig.getButtonHeight(context),
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveConfig.getMediumSpacing(context),
            vertical: ResponsiveConfig.getSmallSpacing(context),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveConfig.getSmallRadius(context)),
          ),
          textStyle: GoogleFonts.cairo(
            fontSize: ResponsiveConfig.getBodyFontSize(context),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          borderSide: BorderSide(color: AppColors.borderLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          borderSide: BorderSide(color: AppColors.borderLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: ResponsiveConfig.getMediumSpacing(context),
          vertical: ResponsiveConfig.getSmallSpacing(context),
        ),
        labelStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          color: AppColors.textSecondary,
        ),
        hintStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          color: AppColors.textSecondary,
        ),
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: ResponsiveConfig.getMediumSpacing(context),
          vertical: ResponsiveConfig.getSmallSpacing(context),
        ),
        titleTextStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
        subtitleTextStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          color: AppColors.textSecondary,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getSmallRadius(context)),
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: AppColors.surface,
        elevation: ResponsiveConfig.getCardElevation(context) * 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getLargeRadius(context)),
        ),
        titleTextStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getHeadingFontSize(context),
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        contentTextStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          color: AppColors.textPrimary,
        ),
      ),

      // Bottom Sheet Theme
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: AppColors.surface,
        elevation: ResponsiveConfig.getCardElevation(context) * 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(ResponsiveConfig.getLargeRadius(context)),
            topRight: Radius.circular(ResponsiveConfig.getLargeRadius(context)),
          ),
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: ResponsiveConfig.getButtonElevation(context),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getMediumRadius(context)),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        elevation: ResponsiveConfig.getCardElevation(context),
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          fontWeight: FontWeight.w400,
        ),
      ),

      // Text Theme
      textTheme: GoogleFonts.cairoTextTheme().copyWith(
        displayLarge: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getTitleFontSize(context) * 1.5,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        displayMedium: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getTitleFontSize(context) * 1.25,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        displaySmall: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getTitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        headlineLarge: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getHeadingFontSize(context) * 1.25,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        headlineMedium: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getHeadingFontSize(context),
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        headlineSmall: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getHeadingFontSize(context) * 0.9,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        bodyLarge: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context) * 1.1,
          color: AppColors.textPrimary,
        ),
        bodyMedium: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          color: AppColors.textPrimary,
        ),
        bodySmall: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          color: AppColors.textSecondary,
        ),
        labelLarge: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getBodyFontSize(context),
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
        labelMedium: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
        labelSmall: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context) * 0.9,
          fontWeight: FontWeight.w400,
          color: AppColors.textSecondary,
        ),
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColors.textPrimary,
        size: ResponsiveConfig.getMediumIconSize(context),
      ),

      // Primary Icon Theme
      primaryIconTheme: IconThemeData(
        color: AppColors.textOnPrimary,
        size: ResponsiveConfig.getMediumIconSize(context),
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.borderLight,
        thickness: 1,
        space: ResponsiveConfig.getSmallSpacing(context),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.surface,
        selectedColor: AppColors.primary,
        disabledColor: AppColors.borderLight,
        labelStyle: GoogleFonts.cairo(
          fontSize: ResponsiveConfig.getCaptionFontSize(context),
          color: AppColors.textPrimary,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveConfig.getSmallRadius(context)),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: ResponsiveConfig.getSmallSpacing(context),
          vertical: ResponsiveConfig.getSmallSpacing(context) / 2,
        ),
      ),
    );
  }
}
