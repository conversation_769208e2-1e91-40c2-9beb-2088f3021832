import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'constants/app_strings.dart';
import 'core/theme/responsive_theme.dart';
import 'providers/app_state_provider.dart';
import 'screens/splash_screen.dart';

void main() {
  runApp(const MishwarakMaanaApp());
}

class MishwarakMaanaApp extends StatelessWidget {
  const MishwarakMaanaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AppStateProvider()),
          ],
          child: Builder(
            builder: (context) => MaterialApp(
              title: AppStrings.appName,
              debugShowCheckedModeBanner: false,
              theme: ResponsiveTheme.getLightTheme(context),
              home: const SplashScreen(),
            ),
          ),
        );
      },
    );
  }
}
