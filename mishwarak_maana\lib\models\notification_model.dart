import 'package:flutter/material.dart';

/// أنواع الإشعارات المختلفة
enum NotificationType {
  newRequest,
  requestAccepted,
  requestRejected,
  serviceCompleted,
  newMessage,
  payment,
  general,
}

/// نموذج الإشعار
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });

  /// إنشاء نسخة محدثة من الإشعار
  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
      'data': data,
    };
  }

  /// إنشاء من Map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => NotificationType.general,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      isRead: map['isRead'] ?? false,
      data: map['data'],
    );
  }

  /// الحصول على أيقونة الإشعار
  IconData get icon {
    switch (type) {
      case NotificationType.newRequest:
        return Icons.assignment;
      case NotificationType.requestAccepted:
        return Icons.check_circle;
      case NotificationType.requestRejected:
        return Icons.cancel;
      case NotificationType.serviceCompleted:
        return Icons.done_all;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.general:
        return Icons.info;
    }
  }

  /// الحصول على لون الإشعار
  Color get color {
    switch (type) {
      case NotificationType.newRequest:
        return const Color(0xFF2196F3);
      case NotificationType.requestAccepted:
        return const Color(0xFF4CAF50);
      case NotificationType.requestRejected:
        return const Color(0xFFF44336);
      case NotificationType.serviceCompleted:
        return const Color(0xFF4CAF50);
      case NotificationType.newMessage:
        return const Color(0xFF03DAC6);
      case NotificationType.payment:
        return const Color(0xFFFFD700);
      case NotificationType.general:
        return const Color(0xFF607D8B);
    }
  }

  /// الحصول على وصف نوع الإشعار
  String get typeDescription {
    switch (type) {
      case NotificationType.newRequest:
        return 'طلب جديد';
      case NotificationType.requestAccepted:
        return 'تم قبول الطلب';
      case NotificationType.requestRejected:
        return 'تم رفض الطلب';
      case NotificationType.serviceCompleted:
        return 'تم إكمال الخدمة';
      case NotificationType.newMessage:
        return 'رسالة جديدة';
      case NotificationType.payment:
        return 'دفع';
      case NotificationType.general:
        return 'عام';
    }
  }

  /// التحقق من كون الإشعار حديث (أقل من 24 ساعة)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    return difference.inHours < 24;
  }

  /// الحصول على نص الوقت المنسق
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead)';
  }
}

/// إشعارات تجريبية للاختبار
class MockNotifications {
  static List<NotificationModel> get sampleNotifications => [
    NotificationModel(
      id: '1',
      title: 'طلب خدمة جديد',
      message: 'لديك طلب خدمة جديد من أحمد محمد لإصلاح السيارة',
      type: NotificationType.newRequest,
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      isRead: false,
    ),
    NotificationModel(
      id: '2',
      title: 'تم قبول طلبك',
      message: 'تم قبول طلب الخدمة الخاص بك من قبل محمد علي',
      type: NotificationType.requestAccepted,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isRead: false,
    ),
    NotificationModel(
      id: '3',
      title: 'تم إكمال الخدمة',
      message: 'تم إكمال خدمة إصلاح الكهرباء بنجاح',
      type: NotificationType.serviceCompleted,
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      isRead: true,
    ),
    NotificationModel(
      id: '4',
      title: 'رسالة جديدة',
      message: 'لديك رسالة جديدة من مقدم الخدمة',
      type: NotificationType.newMessage,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      isRead: true,
    ),
    NotificationModel(
      id: '5',
      title: 'تم الدفع بنجاح',
      message: 'تم استلام الدفعة بقيمة 150 ريال',
      type: NotificationType.payment,
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      isRead: true,
    ),
  ];
}
