import 'user_model.dart';

class ServiceRequestModel {
  final String id;
  final String clientId;
  final String? serviceProviderId;
  final ServiceType serviceType;
  final String title;
  final String description;
  final LocationModel pickupLocation;
  final LocationModel? dropoffLocation;
  final double? estimatedPrice;
  final double? finalPrice;
  final RequestStatus status;
  final PaymentMethod paymentMethod;
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;
  final Map<String, dynamic>? additionalInfo;

  ServiceRequestModel({
    required this.id,
    required this.clientId,
    this.serviceProviderId,
    required this.serviceType,
    required this.title,
    required this.description,
    required this.pickupLocation,
    this.dropoffLocation,
    this.estimatedPrice,
    this.finalPrice,
    required this.status,
    required this.paymentMethod,
    required this.createdAt,
    this.acceptedAt,
    this.completedAt,
    this.additionalInfo,
  });

  factory ServiceRequestModel.fromJson(Map<String, dynamic> json) {
    return ServiceRequestModel(
      id: json['id'],
      clientId: json['clientId'],
      serviceProviderId: json['serviceProviderId'],
      serviceType: ServiceType.values.firstWhere((e) => e.toString() == json['serviceType']),
      title: json['title'],
      description: json['description'],
      pickupLocation: LocationModel.fromJson(json['pickupLocation']),
      dropoffLocation: json['dropoffLocation'] != null 
          ? LocationModel.fromJson(json['dropoffLocation'])
          : null,
      estimatedPrice: json['estimatedPrice']?.toDouble(),
      finalPrice: json['finalPrice']?.toDouble(),
      status: RequestStatus.values.firstWhere((e) => e.toString() == json['status']),
      paymentMethod: PaymentMethod.values.firstWhere((e) => e.toString() == json['paymentMethod']),
      createdAt: DateTime.parse(json['createdAt']),
      acceptedAt: json['acceptedAt'] != null ? DateTime.parse(json['acceptedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      additionalInfo: json['additionalInfo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientId': clientId,
      'serviceProviderId': serviceProviderId,
      'serviceType': serviceType.toString(),
      'title': title,
      'description': description,
      'pickupLocation': pickupLocation.toJson(),
      'dropoffLocation': dropoffLocation?.toJson(),
      'estimatedPrice': estimatedPrice,
      'finalPrice': finalPrice,
      'status': status.toString(),
      'paymentMethod': paymentMethod.toString(),
      'createdAt': createdAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }
}

class LocationModel {
  final double latitude;
  final double longitude;
  final String address;
  final String? landmark;

  LocationModel({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.landmark,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      address: json['address'],
      landmark: json['landmark'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'landmark': landmark,
    };
  }
}

enum RequestStatus {
  pending,
  accepted,
  inProgress,
  completed,
  cancelled,
}

enum PaymentMethod {
  cash,
  electronic,
}

extension RequestStatusExtension on RequestStatus {
  String get displayName {
    switch (this) {
      case RequestStatus.pending:
        return 'في الانتظار';
      case RequestStatus.accepted:
        return 'تم القبول';
      case RequestStatus.inProgress:
        return 'قيد التنفيذ';
      case RequestStatus.completed:
        return 'مكتمل';
      case RequestStatus.cancelled:
        return 'ملغي';
    }
  }
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'دفع نقدي';
      case PaymentMethod.electronic:
        return 'دفع إلكتروني';
    }
  }
}
