class UserModel {
  final String id;
  final String fullName;
  final String email;
  final String phone;
  final UserRole role;
  final ServiceType? serviceType;
  final Map<String, dynamic>? additionalInfo;
  final double? rating;
  final String? profileImage;
  final bool isActive;
  final DateTime createdAt;

  UserModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.role,
    this.serviceType,
    this.additionalInfo,
    this.rating,
    this.profileImage,
    this.isActive = true,
    required this.createdAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      fullName: json['fullName'],
      email: json['email'],
      phone: json['phone'],
      role: UserRole.values.firstWhere((e) => e.toString() == json['role']),
      serviceType: json['serviceType'] != null 
          ? ServiceType.values.firstWhere((e) => e.toString() == json['serviceType'])
          : null,
      additionalInfo: json['additionalInfo'],
      rating: json['rating']?.toDouble(),
      profileImage: json['profileImage'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'role': role.toString(),
      'serviceType': serviceType?.toString(),
      'additionalInfo': additionalInfo,
      'rating': rating,
      'profileImage': profileImage,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

enum UserRole {
  client,
  serviceProvider,
}

enum ServiceType {
  driver,
  electrician,
  plumber,
  carpenter,
  mechanic,
  cleaner,
  painter,
  gardener,
}

extension ServiceTypeExtension on ServiceType {
  String get displayName {
    switch (this) {
      case ServiceType.driver:
        return 'سائق';
      case ServiceType.electrician:
        return 'كهربائي';
      case ServiceType.plumber:
        return 'سباك';
      case ServiceType.carpenter:
        return 'نجار';
      case ServiceType.mechanic:
        return 'ميكانيكي';
      case ServiceType.cleaner:
        return 'عامل نظافة';
      case ServiceType.painter:
        return 'دهان';
      case ServiceType.gardener:
        return 'بستاني';
    }
  }

  String get icon {
    switch (this) {
      case ServiceType.driver:
        return '🚗';
      case ServiceType.electrician:
        return '⚡';
      case ServiceType.plumber:
        return '🔧';
      case ServiceType.carpenter:
        return '🔨';
      case ServiceType.mechanic:
        return '🔧';
      case ServiceType.cleaner:
        return '🧹';
      case ServiceType.painter:
        return '🎨';
      case ServiceType.gardener:
        return '🌱';
    }
  }
}
