import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/service_request_model.dart';

class AppStateProvider extends ChangeNotifier {
  UserModel? _currentUser;
  UserRole? _selectedRole;
  List<ServiceRequestModel> _activeRequests = [];
  List<ServiceRequestModel> _requestHistory = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  UserModel? get currentUser => _currentUser;
  UserRole? get selectedRole => _selectedRole;
  List<ServiceRequestModel> get activeRequests => _activeRequests;
  List<ServiceRequestModel> get requestHistory => _requestHistory;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;

  // User Management
  void setSelectedRole(UserRole role) {
    _selectedRole = role;
    notifyListeners();
  }

  void setCurrentUser(UserModel user) {
    _currentUser = user;
    _selectedRole = user.role;
    notifyListeners();
  }

  void logout() {
    _currentUser = null;
    _selectedRole = null;
    _activeRequests.clear();
    _requestHistory.clear();
    _errorMessage = null;
    notifyListeners();
  }

  // Loading State
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Error Handling
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Service Requests Management
  void addActiveRequest(ServiceRequestModel request) {
    _activeRequests.add(request);
    notifyListeners();
  }

  void updateRequestStatus(String requestId, RequestStatus status) {
    final index = _activeRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      final request = _activeRequests[index];
      final updatedRequest = ServiceRequestModel(
        id: request.id,
        clientId: request.clientId,
        serviceProviderId: request.serviceProviderId,
        serviceType: request.serviceType,
        title: request.title,
        description: request.description,
        pickupLocation: request.pickupLocation,
        dropoffLocation: request.dropoffLocation,
        estimatedPrice: request.estimatedPrice,
        finalPrice: request.finalPrice,
        status: status,
        paymentMethod: request.paymentMethod,
        createdAt: request.createdAt,
        acceptedAt: request.acceptedAt,
        completedAt: status == RequestStatus.completed ? DateTime.now() : request.completedAt,
        additionalInfo: request.additionalInfo,
      );

      _activeRequests[index] = updatedRequest;

      // Move to history if completed or cancelled
      if (status == RequestStatus.completed || status == RequestStatus.cancelled) {
        _activeRequests.removeAt(index);
        _requestHistory.insert(0, updatedRequest);
      }

      notifyListeners();
    }
  }

  void removeActiveRequest(String requestId) {
    _activeRequests.removeWhere((r) => r.id == requestId);
    notifyListeners();
  }

  void setActiveRequests(List<ServiceRequestModel> requests) {
    _activeRequests = requests;
    notifyListeners();
  }

  void setRequestHistory(List<ServiceRequestModel> history) {
    _requestHistory = history;
    notifyListeners();
  }

  // Mock Authentication Methods (Replace with real API calls)
  Future<bool> login(String email, String password, UserRole role, {ServiceType? serviceType}) async {
    setLoading(true);
    clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock user creation
      final user = UserModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        fullName: 'مستخدم تجريبي',
        email: email,
        phone: '+967777777777',
        role: role,
        serviceType: serviceType,
        rating: role == UserRole.serviceProvider ? 4.5 : null,
        createdAt: DateTime.now(),
      );

      setCurrentUser(user);
      setLoading(false);
      return true;
    } catch (e) {
      setError('خطأ في تسجيل الدخول: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  Future<bool> register(Map<String, dynamic> userData) async {
    setLoading(true);
    clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock user creation
      final user = UserModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        fullName: userData['fullName'],
        email: userData['email'],
        phone: userData['phone'] ?? '+967777777777',
        role: userData['role'],
        serviceType: userData['serviceType'],
        additionalInfo: userData['additionalInfo'],
        createdAt: DateTime.now(),
      );

      setCurrentUser(user);
      setLoading(false);
      return true;
    } catch (e) {
      setError('خطأ في التسجيل: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }
}
