import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/user_model.dart';
import '../../providers/app_state_provider.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/responsive_widgets.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/service_type_dropdown.dart';
import '../home/<USER>';
import '../home/<USER>';
import 'register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _vehiclePlateController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _specializationController = TextEditingController();
  final _licenseController = TextEditingController();

  ServiceType? _selectedServiceType;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _vehiclePlateController.dispose();
    _vehicleModelController.dispose();
    _specializationController.dispose();
    _licenseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        final isServiceProvider =
            appState.selectedRole == UserRole.serviceProvider;

        return Scaffold(
          body: ResponsiveContainer(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.background,
                  AppColors.surface,
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                padding: ResponsiveHelper.screenPadding,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ResponsiveSpacing.large(),

                      // Header
                      _buildHeader(isServiceProvider),

                      ResponsiveSpacing.extraLarge(),

                      // Email Field
                      CustomTextField(
                        controller: _emailController,
                        label: AppStrings.email,
                        keyboardType: TextInputType.emailAddress,
                        prefixIcon: Icons.email_outlined,
                        validator: _validateEmail,
                      ),

                      ResponsiveSpacing.medium(),

                      // Password Field
                      CustomTextField(
                        controller: _passwordController,
                        label: AppStrings.password,
                        obscureText: _obscurePassword,
                        prefixIcon: Icons.lock_outline,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.textSecondary,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                        validator: _validatePassword,
                      ),

                      // Service Provider Specific Fields
                      if (isServiceProvider) ...[
                        ResponsiveSpacing.medium(),

                        // Service Type Dropdown
                        ServiceTypeDropdown(
                          value: _selectedServiceType,
                          onChanged: (value) {
                            setState(() {
                              _selectedServiceType = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return AppStrings.fieldRequired;
                            }
                            return null;
                          },
                        ),

                        // Additional fields based on service type
                        if (_selectedServiceType != null)
                          ..._buildAdditionalFields(),
                      ],

                      ResponsiveSpacing.extraLarge(),

                      // Login Button
                      CustomButton(
                        text: AppStrings.login,
                        onPressed: appState.isLoading ? null : _handleLogin,
                        isLoading: appState.isLoading,
                      ),

                      ResponsiveSpacing.large(),

                      // Forgot Password
                      TextButton(
                        onPressed: () {},
                        child: ResponsiveText(
                          AppStrings.forgotPassword,
                          type: ResponsiveTextType.body,
                          style: TextStyle(
                            color: AppColors.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),

                      ResponsiveSpacing.large(),

                      // Register Link
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ResponsiveText(
                            'ليس لديك حساب؟ ',
                            type: ResponsiveTextType.body,
                            style: TextStyle(color: AppColors.textSecondary),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const RegisterScreen(),
                                ),
                              );
                            },
                            child: ResponsiveText(
                              'سجل الآن',
                              type: ResponsiveTextType.body,
                              style: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      ResponsiveSpacing.extraLarge(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isServiceProvider) {
    return Column(
      children: [
        // Back Button
        Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppColors.textPrimary,
                size: ResponsiveHelper.mediumIconSize,
              ),
            ),
          ],
        ),

        ResponsiveSpacing.medium(),

        // Title
        ResponsiveText(
          AppStrings.login,
          type: ResponsiveTextType.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),

        ResponsiveSpacing.small(),

        // Role Indicator with enhanced design
        ResponsiveContainer(
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.mediumSpacing,
            vertical: ResponsiveHelper.smallSpacing,
          ),
          decoration: BoxDecoration(
            color: isServiceProvider
                ? AppColors.secondary.withValues(alpha: 0.1)
                : AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(ResponsiveHelper.largeRadius),
            border: Border.all(
              color: isServiceProvider
                  ? AppColors.secondary.withValues(alpha: 0.3)
                  : AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isServiceProvider ? Icons.work_outline : Icons.person_outline,
                color:
                    isServiceProvider ? AppColors.secondary : AppColors.primary,
                size: ResponsiveHelper.smallIconSize,
              ),
              SizedBox(width: ResponsiveHelper.smallSpacing),
              ResponsiveText(
                isServiceProvider
                    ? AppStrings.serviceProvider
                    : AppStrings.client,
                type: ResponsiveTextType.caption,
                style: TextStyle(
                  color: isServiceProvider
                      ? AppColors.secondary
                      : AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildAdditionalFields() {
    if (_selectedServiceType == ServiceType.driver) {
      return [
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _vehiclePlateController,
          label: AppStrings.vehiclePlateNumber,
          prefixIcon: Icons.directions_car,
        ),
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _vehicleModelController,
          label: AppStrings.vehicleModel,
          prefixIcon: Icons.car_rental,
        ),
      ];
    } else {
      return [
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _specializationController,
          label: AppStrings.specialization,
          prefixIcon: Icons.work_outline,
        ),
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _licenseController,
          label: '${AppStrings.licenseNumber} (اختياري)',
          prefixIcon: Icons.card_membership,
        ),
      ];
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return AppStrings.invalidEmail;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value.length < 6) {
      return AppStrings.passwordTooShort;
    }
    return null;
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appState = context.read<AppStateProvider>();

    final success = await appState.login(
      _emailController.text.trim(),
      _passwordController.text,
      appState.selectedRole!,
      serviceType: _selectedServiceType,
    );

    if (success && mounted) {
      // Navigate to appropriate home screen
      if (appState.selectedRole == UserRole.client) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ClientHomeScreen(),
          ),
        );
      } else {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ServiceProviderHomeScreen(),
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(appState.errorMessage ?? 'خطأ في تسجيل الدخول'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
