import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/user_model.dart';
import '../../providers/app_state_provider.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/responsive_widgets.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/service_type_dropdown.dart';
import '../home/<USER>';
import '../home/<USER>';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _vehiclePlateController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _specializationController = TextEditingController();
  final _licenseController = TextEditingController();

  ServiceType? _selectedServiceType;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _vehiclePlateController.dispose();
    _vehicleModelController.dispose();
    _specializationController.dispose();
    _licenseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        final isServiceProvider =
            appState.selectedRole == UserRole.serviceProvider;

        return Scaffold(
          body: ResponsiveContainer(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.background,
                  AppColors.surface,
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                padding: ResponsiveHelper.screenPadding,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ResponsiveSpacing.large(),

                      // Header
                      _buildHeader(isServiceProvider),

                      ResponsiveSpacing.extraLarge(),

                      // Full Name Field
                      CustomTextField(
                        controller: _fullNameController,
                        label: AppStrings.fullName,
                        prefixIcon: Icons.person_outline,
                        validator: _validateRequired,
                      ),

                      ResponsiveSpacing.medium(),

                      // Email Field
                      CustomTextField(
                        controller: _emailController,
                        label: AppStrings.email,
                        keyboardType: TextInputType.emailAddress,
                        prefixIcon: Icons.email_outlined,
                        validator: _validateEmail,
                      ),

                      ResponsiveSpacing.medium(),

                      // Phone Field
                      CustomTextField(
                        controller: _phoneController,
                        label: AppStrings.phone,
                        keyboardType: TextInputType.phone,
                        prefixIcon: Icons.phone_outlined,
                        validator: _validatePhone,
                      ),

                      ResponsiveSpacing.medium(),

                      // Password Field
                      CustomTextField(
                        controller: _passwordController,
                        label: AppStrings.password,
                        obscureText: _obscurePassword,
                        prefixIcon: Icons.lock_outline,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.textSecondary,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                        validator: _validatePassword,
                      ),

                      ResponsiveSpacing.medium(),

                      // Confirm Password Field
                      CustomTextField(
                        controller: _confirmPasswordController,
                        label: AppStrings.confirmPassword,
                        obscureText: _obscureConfirmPassword,
                        prefixIcon: Icons.lock_outline,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.textSecondary,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureConfirmPassword =
                                  !_obscureConfirmPassword;
                            });
                          },
                        ),
                        validator: _validateConfirmPassword,
                      ),

                      // Service Provider Specific Fields
                      if (isServiceProvider) ...[
                        ResponsiveSpacing.medium(),

                        // Service Type Dropdown
                        ServiceTypeDropdown(
                          value: _selectedServiceType,
                          onChanged: (value) {
                            setState(() {
                              _selectedServiceType = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return AppStrings.fieldRequired;
                            }
                            return null;
                          },
                        ),

                        // Additional fields based on service type
                        if (_selectedServiceType != null)
                          ..._buildAdditionalFields(),
                      ],

                      ResponsiveSpacing.extraLarge(),

                      // Register Button
                      CustomButton(
                        text: AppStrings.register,
                        onPressed: appState.isLoading ? null : _handleRegister,
                        isLoading: appState.isLoading,
                      ),

                      ResponsiveSpacing.large(),

                      // Login Link
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ResponsiveText(
                            'لديك حساب بالفعل؟ ',
                            type: ResponsiveTextType.body,
                            style: TextStyle(color: AppColors.textSecondary),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: ResponsiveText(
                              'سجل دخول',
                              type: ResponsiveTextType.body,
                              style: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      ResponsiveSpacing.extraLarge(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isServiceProvider) {
    return Column(
      children: [
        // Back Button
        Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_ios,
                color: AppColors.textPrimary,
                size: ResponsiveHelper.mediumIconSize,
              ),
            ),
          ],
        ),

        ResponsiveSpacing.medium(),

        // Title
        ResponsiveText(
          AppStrings.register,
          type: ResponsiveTextType.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),

        ResponsiveSpacing.small(),

        // Role Indicator
        ResponsiveContainer(
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.mediumSpacing,
            vertical: ResponsiveHelper.smallSpacing,
          ),
          decoration: BoxDecoration(
            color: isServiceProvider
                ? AppColors.secondary.withValues(alpha: 0.1)
                : AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(ResponsiveHelper.largeRadius),
            border: Border.all(
              color: isServiceProvider
                  ? AppColors.secondary.withValues(alpha: 0.3)
                  : AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isServiceProvider ? Icons.work_outline : Icons.person_outline,
                color:
                    isServiceProvider ? AppColors.secondary : AppColors.primary,
                size: ResponsiveHelper.smallIconSize,
              ),
              SizedBox(width: ResponsiveHelper.smallSpacing),
              ResponsiveText(
                isServiceProvider
                    ? AppStrings.serviceProvider
                    : AppStrings.client,
                type: ResponsiveTextType.caption,
                style: TextStyle(
                  color: isServiceProvider
                      ? AppColors.secondary
                      : AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildAdditionalFields() {
    if (_selectedServiceType == ServiceType.driver) {
      return [
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _vehiclePlateController,
          label: AppStrings.vehiclePlateNumber,
          prefixIcon: Icons.directions_car,
          validator: _validateRequired,
        ),
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _vehicleModelController,
          label: AppStrings.vehicleModel,
          prefixIcon: Icons.car_rental,
          validator: _validateRequired,
        ),
      ];
    } else {
      return [
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _specializationController,
          label: AppStrings.specialization,
          prefixIcon: Icons.work_outline,
          validator: _validateRequired,
        ),
        ResponsiveSpacing.medium(),
        CustomTextField(
          controller: _licenseController,
          label: '${AppStrings.licenseNumber} (اختياري)',
          prefixIcon: Icons.card_membership,
        ),
      ];
    }
  }

  String? _validateRequired(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return AppStrings.invalidEmail;
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (!RegExp(r'^\+?[0-9]{9,15}$').hasMatch(value)) {
      return AppStrings.invalidPhone;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value.length < 6) {
      return AppStrings.passwordTooShort;
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value != _passwordController.text) {
      return AppStrings.passwordsNotMatch;
    }
    return null;
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appState = context.read<AppStateProvider>();

    // Prepare additional info based on service type
    Map<String, dynamic>? additionalInfo;
    if (appState.selectedRole == UserRole.serviceProvider &&
        _selectedServiceType != null) {
      if (_selectedServiceType == ServiceType.driver) {
        additionalInfo = {
          'vehiclePlateNumber': _vehiclePlateController.text.trim(),
          'vehicleModel': _vehicleModelController.text.trim(),
        };
      } else {
        additionalInfo = {
          'specialization': _specializationController.text.trim(),
          'licenseNumber': _licenseController.text.trim(),
        };
      }
    }

    final userData = {
      'fullName': _fullNameController.text.trim(),
      'email': _emailController.text.trim(),
      'phone': _phoneController.text.trim(),
      'password': _passwordController.text,
      'role': appState.selectedRole!,
      'serviceType': _selectedServiceType,
      'additionalInfo': additionalInfo,
    };

    final success = await appState.register(userData);

    if (success && mounted) {
      // Navigate to appropriate home screen
      if (appState.selectedRole == UserRole.client) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ClientHomeScreen(),
          ),
        );
      } else {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ServiceProviderHomeScreen(),
          ),
        );
      }
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(appState.errorMessage ?? 'خطأ في التسجيل'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
