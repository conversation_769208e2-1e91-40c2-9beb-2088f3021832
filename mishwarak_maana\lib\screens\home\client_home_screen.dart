import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/user_model.dart';
import '../../providers/app_state_provider.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/responsive_widgets.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/enhanced_icon.dart';
import '../notifications/notifications_screen.dart';
import '../request/create_request_screen.dart';

class ClientHomeScreen extends StatefulWidget {
  const ClientHomeScreen({super.key});

  @override
  State<ClientHomeScreen> createState() => _ClientHomeScreenState();
}

class _ClientHomeScreenState extends State<ClientHomeScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: _buildAppBar(appState),
          drawer: _buildDrawer(appState),
          body: _buildBody(),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AppStateProvider appState) {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            '${AppStrings.welcome}، ${appState.currentUser?.fullName ?? 'مستخدم'}',
            type: ResponsiveTextType.body,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textOnPrimary,
            ),
          ),
          ResponsiveText(
            'كيف يمكننا مساعدتك اليوم؟',
            type: ResponsiveTextType.caption,
            style: TextStyle(
              color: AppColors.textOnPrimary.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const NotificationsScreen(),
              ),
            );
          },
          icon: Stack(
            children: [
              NavigationIcon(
                icon: Icons.notifications_outlined,
                label: 'الإشعارات',
                size: ResponsiveHelper.mediumIconSize,
              ),
              // Notification badge
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: ResponsiveHelper.getResponsiveValue(
                    mobile: 8,
                    tablet: 10,
                    desktop: 12,
                  ),
                  height: ResponsiveHelper.getResponsiveValue(
                    mobile: 8,
                    tablet: 10,
                    desktop: 12,
                  ),
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: ResponsiveHelper.smallSpacing),
      ],
    );
  }

  Widget _buildDrawer(AppStateProvider appState) {
    return Drawer(
      child: Column(
        children: [
          // Drawer Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: AppColors.surface,
                    child: Icon(
                      Icons.person,
                      size: 24.sp,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    appState.currentUser?.fullName ?? 'مستخدم',
                    style: GoogleFonts.cairo(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  Text(
                    appState.currentUser?.email ?? '',
                    style: GoogleFonts.cairo(
                      fontSize: 12.sp,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Drawer Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.person_outline,
                  title: AppStrings.profile,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.history,
                  title: AppStrings.requestHistory,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.settings_outlined,
                  title: AppStrings.settings,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.support_agent_outlined,
                  title: AppStrings.support,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.info_outline,
                  title: AppStrings.about,
                  onTap: () {},
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: AppStrings.logout,
                  onTap: () {
                    _showLogoutDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: NavigationIcon(
        icon: icon,
        label: title,
        size: ResponsiveHelper.getResponsiveValue(
          mobile: 20,
          tablet: 22,
          desktop: 24,
        ),
        onTap: onTap,
      ),
      title: ResponsiveText(
        title,
        type: ResponsiveTextType.body,
        style: const TextStyle(color: AppColors.textPrimary),
      ),
      onTap: onTap,
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildServicesTab();
      case 2:
        return _buildRequestsTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Actions
          _buildQuickActions(),

          SizedBox(height: 30.h),

          // Available Services
          _buildSectionTitle(AppStrings.availableServices),
          SizedBox(height: 16.h),
          _buildServicesGrid(),

          SizedBox(height: 30.h),

          // Active Requests
          _buildSectionTitle(AppStrings.activeRequests),
          SizedBox(height: 16.h),
          _buildActiveRequests(),
        ],
      ),
    );
  }

  Widget _buildServicesTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('جميع الخدمات'),
          SizedBox(height: 16.h),
          _buildServicesGrid(),
        ],
      ),
    );
  }

  Widget _buildRequestsTab() {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        if (appState.activeRequests.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 16.h),
                Text(
                  'لا توجد طلبات نشطة',
                  style: GoogleFonts.cairo(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(24.w),
          itemCount: appState.activeRequests.length,
          itemBuilder: (context, index) {
            final request = appState.activeRequests[index];
            return _buildRequestCard(request);
          },
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: AppStrings.requestRide,
            icon: Icons.directions_car,
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateRequestScreen(
                    preSelectedServiceType: ServiceType.driver,
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: CustomButton(
            text: AppStrings.requestService,
            icon: Icons.build,
            outlined: true,
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateRequestScreen(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildServicesGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 1.2,
      ),
      itemCount: ServiceType.values.length,
      itemBuilder: (context, index) {
        final serviceType = ServiceType.values[index];
        return _buildServiceCard(serviceType);
      },
    );
  }

  Widget _buildServiceCard(ServiceType serviceType) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CreateRequestScreen(
              preSelectedServiceType: serviceType,
            ),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              serviceType.icon,
              style: TextStyle(fontSize: 24.sp),
            ),
            SizedBox(height: 8.h),
            Text(
              serviceType.displayName,
              style: GoogleFonts.cairo(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveRequests() {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        if (appState.activeRequests.isEmpty) {
          return Container(
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: AppColors.borderLight,
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                'لا توجد طلبات نشطة',
                style: GoogleFonts.cairo(
                  fontSize: 16.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          );
        }

        return Column(
          children: appState.activeRequests
              .take(3)
              .map((request) => _buildRequestCard(request))
              .toList(),
        );
      },
    );
  }

  Widget _buildRequestCard(dynamic request) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طلب خدمة',
                style: GoogleFonts.cairo(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'في الانتظار',
                  style: GoogleFonts.cairo(
                    fontSize: 12.sp,
                    color: AppColors.warning,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'تم إرسال طلبك بنجاح وننتظر موافقة مقدم الخدمة',
            style: GoogleFonts.cairo(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppColors.surface,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.textSecondary,
      selectedLabelStyle: GoogleFonts.cairo(
        fontSize: 11.sp,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.cairo(
        fontSize: 11.sp,
      ),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home_outlined),
          activeIcon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.grid_view_outlined),
          activeIcon: Icon(Icons.grid_view),
          label: 'الخدمات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.list_alt_outlined),
          activeIcon: Icon(Icons.list_alt),
          label: 'طلباتي',
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تسجيل الخروج',
          style: GoogleFonts.cairo(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
          style: GoogleFonts.cairo(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppStrings.cancel,
              style: GoogleFonts.cairo(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppStateProvider>().logout();
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/',
                (route) => false,
              );
            },
            child: Text(
              AppStrings.logout,
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
