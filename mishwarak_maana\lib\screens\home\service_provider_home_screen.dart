import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/user_model.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/custom_button.dart';

class ServiceProviderHomeScreen extends StatefulWidget {
  const ServiceProviderHomeScreen({super.key});

  @override
  State<ServiceProviderHomeScreen> createState() =>
      _ServiceProviderHomeScreenState();
}

class _ServiceProviderHomeScreenState extends State<ServiceProviderHomeScreen> {
  bool _isAvailable = true;
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: _buildAppBar(appState),
          drawer: _buildDrawer(appState),
          body: _buildBody(),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AppStateProvider appState) {
    return AppBar(
      backgroundColor: AppColors.secondary,
      foregroundColor: AppColors.textOnPrimary,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${AppStrings.welcome}، ${appState.currentUser?.fullName ?? 'مقدم خدمة'}',
            style: GoogleFonts.cairo(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textOnPrimary,
            ),
          ),
          Text(
            appState.currentUser?.serviceType?.displayName ?? 'مقدم خدمة',
            style: GoogleFonts.cairo(
              fontSize: 12.sp,
              color: AppColors.textOnPrimary.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
      actions: [
        // Availability Toggle
        Container(
          margin: EdgeInsets.only(right: 16.w),
          child: Row(
            children: [
              Text(
                _isAvailable ? AppStrings.available : AppStrings.unavailable,
                style: GoogleFonts.cairo(
                  fontSize: 12.sp,
                  color: AppColors.textOnPrimary,
                ),
              ),
              SizedBox(width: 8.w),
              Switch(
                value: _isAvailable,
                onChanged: (value) {
                  setState(() {
                    _isAvailable = value;
                  });
                },
                activeColor: AppColors.success,
                inactiveThumbColor: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDrawer(AppStateProvider appState) {
    return Drawer(
      child: Column(
        children: [
          // Drawer Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: const BoxDecoration(
              gradient: AppColors.secondaryGradient,
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30.r,
                    backgroundColor: AppColors.surface,
                    child: Text(
                      appState.currentUser?.serviceType?.icon ?? '👤',
                      style: TextStyle(fontSize: 24.sp),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    appState.currentUser?.fullName ?? 'مقدم خدمة',
                    style: GoogleFonts.cairo(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  Text(
                    appState.currentUser?.serviceType?.displayName ?? '',
                    style: GoogleFonts.cairo(
                      fontSize: 14.sp,
                      // ignore: deprecated_member_use
                      color: AppColors.textOnPrimary.withOpacity(0.8),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.accent,
                        size: 16.sp,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        // ignore: unnecessary_string_interpolations
                        '${appState.currentUser?.rating?.toStringAsFixed(1) ?? '0.0'}',
                        style: GoogleFonts.cairo(
                          fontSize: 14.sp,
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Drawer Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.person_outline,
                  title: AppStrings.profile,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.attach_money,
                  title: AppStrings.earnings,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.history,
                  title: 'سجل الخدمات',
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.settings_outlined,
                  title: AppStrings.settings,
                  onTap: () {},
                ),
                _buildDrawerItem(
                  icon: Icons.support_agent_outlined,
                  title: AppStrings.support,
                  onTap: () {},
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: AppStrings.logout,
                  onTap: () {
                    _showLogoutDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.textSecondary,
        size: 24.sp,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16.sp,
          color: AppColors.textPrimary,
        ),
      ),
      onTap: onTap,
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildRequestsTab();
      case 2:
        return _buildEarningsTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          _buildStatusCard(),

          SizedBox(height: 30.h),

          // New Requests
          _buildSectionTitle(AppStrings.newRequests),
          SizedBox(height: 16.h),
          _buildNewRequests(),

          SizedBox(height: 30.h),

          // Today's Summary
          _buildSectionTitle('ملخص اليوم'),
          SizedBox(height: 16.h),
          _buildTodaySummary(),
        ],
      ),
    );
  }

  Widget _buildRequestsTab() {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        if (appState.activeRequests.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 64.sp,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 16.h),
                Text(
                  'لا توجد طلبات جديدة',
                  style: GoogleFonts.cairo(
                    fontSize: 18.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'ستظهر الطلبات الجديدة هنا',
                  style: GoogleFonts.cairo(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(24.w),
          itemCount: appState.activeRequests.length,
          itemBuilder: (context, index) {
            final request = appState.activeRequests[index];
            return _buildRequestCard(request);
          },
        );
      },
    );
  }

  Widget _buildEarningsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Earnings Summary
          _buildEarningsSummary(),

          SizedBox(height: 30.h),

          // Recent Transactions
          _buildSectionTitle('المعاملات الأخيرة'),
          SizedBox(height: 16.h),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: _isAvailable
            ? AppColors.primaryGradient
            : LinearGradient(
                colors: [
                  AppColors.textSecondary,
                  AppColors.textSecondary.withValues(alpha: 0.8)
                ],
              ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 10.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            _isAvailable ? Icons.check_circle : Icons.pause_circle,
            size: 48.sp,
            color: AppColors.textOnPrimary,
          ),
          SizedBox(height: 12.h),
          Text(
            _isAvailable ? 'أنت متاح للعمل' : 'أنت غير متاح حالياً',
            style: GoogleFonts.cairo(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textOnPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _isAvailable
                ? 'يمكن للعملاء رؤيتك وإرسال طلبات إليك'
                : 'لن تتلقى طلبات جديدة',
            style: GoogleFonts.cairo(
              fontSize: 14.sp,
              // ignore: deprecated_member_use
              color: AppColors.textOnPrimary.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildNewRequests() {
    // Mock data for demonstration
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.notifications_none,
              size: 48.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 12.h),
            Text(
              'لا توجد طلبات جديدة',
              style: GoogleFonts.cairo(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestCard(dynamic request) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طلب خدمة جديد',
                style: GoogleFonts.cairo(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                '15 ر.س',
                style: GoogleFonts.cairo(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'العميل يحتاج إلى خدمة في المنطقة القريبة',
            style: GoogleFonts.cairo(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: AppStrings.acceptRequest,
                  onPressed: () {},
                  height: 40.h,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: CustomButton(
                  text: AppStrings.declineRequest,
                  outlined: true,
                  onPressed: () {},
                  height: 40.h,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTodaySummary() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            title: 'الطلبات المكتملة',
            value: '5',
            icon: Icons.check_circle_outline,
            color: AppColors.success,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildSummaryCard(
            title: 'الأرباح اليوم',
            value: '75 ر.س',
            icon: Icons.attach_money,
            color: AppColors.accent,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 24.sp,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsSummary() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: AppColors.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 10.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجمالي الأرباح',
            style: GoogleFonts.cairo(
              fontSize: 16.sp,
              // ignore: deprecated_member_use
              color: AppColors.textOnPrimary.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '1,250 ر.س',
            style: GoogleFonts.cairo(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textOnPrimary,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'هذا الأسبوع',
                    style: GoogleFonts.cairo(
                      fontSize: 12.sp,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '320 ر.س',
                    style: GoogleFonts.cairo(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'هذا الشهر',
                    style: GoogleFonts.cairo(
                      fontSize: 12.sp,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '890 ر.س',
                    style: GoogleFonts.cairo(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 48.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 12.h),
            Text(
              'لا توجد معاملات حديثة',
              style: GoogleFonts.cairo(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppColors.surface,
      selectedItemColor: AppColors.secondary,
      unselectedItemColor: AppColors.textSecondary,
      selectedLabelStyle: GoogleFonts.cairo(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: GoogleFonts.cairo(
        fontSize: 12.sp,
      ),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home_outlined),
          activeIcon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.list_alt_outlined),
          activeIcon: Icon(Icons.list_alt),
          label: 'الطلبات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.attach_money_outlined),
          activeIcon: Icon(Icons.attach_money),
          label: 'الأرباح',
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تسجيل الخروج',
          style: GoogleFonts.cairo(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
          style: GoogleFonts.cairo(fontSize: 16.sp),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppStrings.cancel,
              style: GoogleFonts.cairo(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppStateProvider>().logout();
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/',
                (route) => false,
              );
            },
            child: Text(
              AppStrings.logout,
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
