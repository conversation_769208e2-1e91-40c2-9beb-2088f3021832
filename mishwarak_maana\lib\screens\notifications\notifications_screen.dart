import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../constants/icon_colors.dart';
import '../../models/notification_model.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/responsive_widgets.dart';
import '../../widgets/enhanced_icon.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  List<NotificationModel> _notifications = [];

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  void _loadNotifications() {
    // Mock notifications for demonstration
    setState(() {
      _notifications.addAll([
        NotificationModel(
          id: '1',
          title: 'طلب جديد',
          message: 'لديك طلب خدمة جديد في منطقة الحصبة',
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
          isRead: false,
          type: NotificationType.newRequest,
        ),
        NotificationModel(
          id: '2',
          title: 'تم قبول الطلب',
          message: 'تم قبول طلبك، مقدم الخدمة في الطريق إليك',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          isRead: true,
          type: NotificationType.requestAccepted,
        ),
        NotificationModel(
          id: '3',
          title: 'تم إكمال الخدمة',
          message: 'تم إكمال خدمتك بنجاح، يرجى تقييم مقدم الخدمة',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          isRead: true,
          type: NotificationType.serviceCompleted,
        ),
        NotificationModel(
          id: '4',
          title: 'رسالة جديدة',
          message: 'لديك رسالة جديدة من مقدم الخدمة',
          timestamp: DateTime.now().subtract(const Duration(days: 1)),
          isRead: false,
          type: NotificationType.newMessage,
        ),
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final unreadCount = _notifications.where((n) => !n.isRead).length;

    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      title: Text(
        AppStrings.notifications,
        style: GoogleFonts.cairo(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        if (unreadCount > 0)
          TextButton(
            onPressed: _markAllAsRead,
            child: Text(
              'تحديد الكل كمقروء',
              style: GoogleFonts.cairo(
                fontSize: 14.sp,
                color: AppColors.textOnPrimary,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBody() {
    if (_notifications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _notifications.length,
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 24.h),
          Text(
            AppStrings.noNotifications,
            style: GoogleFonts.cairo(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: GoogleFonts.cairo(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: notification.isRead
            ? AppColors.surface
            : AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: notification.isRead
              ? AppColors.borderLight
              : AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: EdgeInsets.all(16.w),
        leading: _buildNotificationIcon(notification.type),
        title: Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: GoogleFonts.cairo(
                  fontSize: 16.sp,
                  fontWeight:
                      notification.isRead ? FontWeight.w600 : FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            if (!notification.isRead)
              Container(
                width: 8.w,
                height: 8.w,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8.h),
            Text(
              notification.message,
              style: GoogleFonts.cairo(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              _formatTimestamp(notification.timestamp),
              style: GoogleFonts.cairo(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        onTap: () => _handleNotificationTap(notification),
        trailing: PopupMenuButton<String>(
          icon: EnhancedIcon(
            icon: Icons.more_vert,
            color: AppColors.textSecondary,
            size: ResponsiveHelper.getResponsiveValue(
              mobile: 20,
              tablet: 22,
              desktop: 24,
            ),
            isAnimated: true,
            hasShadow: false,
            hasGlow: false,
          ),
          onSelected: (value) => _handleMenuAction(value, notification),
          itemBuilder: (context) => [
            if (!notification.isRead)
              PopupMenuItem(
                value: 'mark_read',
                child: Text(
                  AppStrings.markAsRead,
                  style: GoogleFonts.cairo(fontSize: 14.sp),
                ),
              ),
            PopupMenuItem(
              value: 'delete',
              child: Text(
                AppStrings.delete,
                style: GoogleFonts.cairo(
                  fontSize: 14.sp,
                  color: AppColors.error,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationType type) {
    IconData icon;
    Color color;

    switch (type) {
      case NotificationType.newRequest:
        icon = Icons.assignment;
        color = IconColors.primaryBlue;
        break;
      case NotificationType.requestAccepted:
        icon = Icons.check_circle;
        color = IconColors.successGreen;
        break;
      case NotificationType.requestRejected:
        icon = Icons.cancel;
        color = IconColors.errorRed;
        break;
      case NotificationType.serviceCompleted:
        icon = Icons.done_all;
        color = IconColors.successGreen;
        break;
      case NotificationType.newMessage:
        icon = Icons.message;
        color = IconColors.secondaryBlue;
        break;
      case NotificationType.payment:
        icon = Icons.payment;
        color = IconColors.goldYellow;
        break;
      case NotificationType.general:
        icon = Icons.info;
        color = IconColors.settingsIcon;
        break;
    }

    return EnhancedIcon(
      icon: icon,
      color: Colors.white,
      size: ResponsiveHelper.getResponsiveValue(
        mobile: 24,
        tablet: 26,
        desktop: 28,
      ),
      backgroundColor: color,
      backgroundSize: ResponsiveHelper.getResponsiveValue(
        mobile: 48,
        tablet: 52,
        desktop: 56,
      ),
      borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
      hasShadow: true,
      hasGlow: false,
      isAnimated: true,
      customShadows: [
        BoxShadow(
          color: color.withValues(alpha: 0.2),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    if (!notification.isRead) {
      setState(() {
        notification.isRead = true;
      });
    }

    // Navigate based on notification type
    switch (notification.type) {
      case NotificationType.newRequest:
        break;
      case NotificationType.requestAccepted:
      case NotificationType.requestRejected:
      case NotificationType.serviceCompleted:
        break;
      case NotificationType.newMessage:
        break;
      case NotificationType.payment:
        break;
      case NotificationType.general:
        // Show notification details
        break;
    }
  }

  void _handleMenuAction(String action, NotificationModel notification) {
    switch (action) {
      case 'mark_read':
        setState(() {
          notification.isRead = true;
        });
        break;
      case 'delete':
        setState(() {
          _notifications.remove(notification);
        });
        break;
    }
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification.isRead = true;
      }
    });
  }
}

class NotificationModel {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  bool isRead;
  final NotificationType type;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.isRead,
    required this.type,
  });
}

enum NotificationType {
  newRequest,
  requestAccepted,
  requestRejected,
  serviceCompleted,
  newMessage,
  payment,
  general,
}
