import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/user_model.dart';
import '../../models/service_request_model.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/service_type_dropdown.dart';

class CreateRequestScreen extends StatefulWidget {
  final ServiceType? preSelectedServiceType;

  const CreateRequestScreen({
    super.key,
    this.preSelectedServiceType,
  });

  @override
  State<CreateRequestScreen> createState() => _CreateRequestScreenState();
}

class _CreateRequestScreenState extends State<CreateRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _pickupLocationController = TextEditingController();
  final _dropoffLocationController = TextEditingController();
  
  ServiceType? _selectedServiceType;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  bool _needsDropoffLocation = false;

  @override
  void initState() {
    super.initState();
    _selectedServiceType = widget.preSelectedServiceType;
    _needsDropoffLocation = _selectedServiceType == ServiceType.driver;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _pickupLocationController.dispose();
    _dropoffLocationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Service Type Selection
              ServiceTypeDropdown(
                value: _selectedServiceType,
                onChanged: (value) {
                  setState(() {
                    _selectedServiceType = value;
                    _needsDropoffLocation = value == ServiceType.driver;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return AppStrings.fieldRequired;
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 24.h),
              
              // Title Field
              CustomTextField(
                controller: _titleController,
                label: 'عنوان الطلب',
                hint: 'مثال: إصلاح مكيف الهواء',
                prefixIcon: Icons.title,
                validator: _validateRequired,
              ),
              
              SizedBox(height: 20.h),
              
              // Description Field
              CustomTextField(
                controller: _descriptionController,
                label: AppStrings.description,
                hint: 'اشرح تفاصيل الخدمة المطلوبة...',
                prefixIcon: Icons.description,
                maxLines: 4,
                validator: _validateRequired,
              ),
              
              SizedBox(height: 24.h),
              
              // Location Section
              _buildLocationSection(),
              
              SizedBox(height: 24.h),
              
              // Payment Method Section
              _buildPaymentMethodSection(),
              
              SizedBox(height: 32.h),
              
              // Submit Button
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return CustomButton(
                    text: 'إرسال الطلب',
                    onPressed: appState.isLoading ? null : _submitRequest,
                    isLoading: appState.isLoading,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      title: Text(
        'طلب خدمة جديد',
        style: GoogleFonts.cairo(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الموقع',
            style: GoogleFonts.cairo(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Pickup Location
          CustomTextField(
            controller: _pickupLocationController,
            label: _needsDropoffLocation 
                ? AppStrings.pickupLocation 
                : 'موقع الخدمة',
            hint: 'اختر الموقع من الخريطة أو اكتبه',
            prefixIcon: Icons.location_on,
            readOnly: true,
            onTap: _selectLocation,
            validator: _validateRequired,
          ),
          
          // Dropoff Location (for rides)
          if (_needsDropoffLocation) ...[
            SizedBox(height: 16.h),
            CustomTextField(
              controller: _dropoffLocationController,
              label: AppStrings.dropoffLocation,
              hint: 'اختر وجهتك من الخريطة أو اكتبها',
              prefixIcon: Icons.flag,
              readOnly: true,
              onTap: _selectDropoffLocation,
              validator: _validateRequired,
            ),
          ],
          
          SizedBox(height: 16.h),
          
          // Current Location Button
          OutlinedButton.icon(
            onPressed: _useCurrentLocation,
            icon: Icon(
              Icons.my_location,
              size: 20.sp,
              color: AppColors.primary,
            ),
            label: Text(
              'استخدام موقعي الحالي',
              style: GoogleFonts.cairo(
                fontSize: 14.sp,
                color: AppColors.primary,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.paymentMethod,
            style: GoogleFonts.cairo(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Cash Payment Option
          RadioListTile<PaymentMethod>(
            value: PaymentMethod.cash,
            groupValue: _selectedPaymentMethod,
            onChanged: (value) {
              setState(() {
                _selectedPaymentMethod = value!;
              });
            },
            title: Row(
              children: [
                Icon(
                  Icons.money,
                  color: AppColors.success,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  AppStrings.payCash,
                  style: GoogleFonts.cairo(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            subtitle: Text(
              'ادفع نقداً عند إكمال الخدمة',
              style: GoogleFonts.cairo(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
            ),
            contentPadding: EdgeInsets.zero,
          ),
          
          // Electronic Payment Option
          RadioListTile<PaymentMethod>(
            value: PaymentMethod.electronic,
            groupValue: _selectedPaymentMethod,
            onChanged: (value) {
              setState(() {
                _selectedPaymentMethod = value!;
              });
            },
            title: Row(
              children: [
                Icon(
                  Icons.credit_card,
                  color: AppColors.info,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  AppStrings.payElectronic,
                  style: GoogleFonts.cairo(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            subtitle: Text(
              'ادفع إلكترونياً (غير متاح حالياً)',
              style: GoogleFonts.cairo(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
            ),
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  String? _validateRequired(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    return null;
  }

  void _selectLocation() {
    
    // For now, just set a mock location
    _pickupLocationController.text = 'شارع الزبيري، الحصبة، إب';
  }

  void _selectDropoffLocation() {
    
    // For now, just set a mock location
    _dropoffLocationController.text = 'شارع جمال عبد الناصر، المدينة، إب';
  }

  void _useCurrentLocation() {

    // For now, just set a mock location
    _pickupLocationController.text = 'موقعي الحالي - الحصبة، إب';
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appState = context.read<AppStateProvider>();
    
    // Create mock request
    final request = ServiceRequestModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      clientId: appState.currentUser!.id,
      serviceType: _selectedServiceType!,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      pickupLocation: LocationModel(
        latitude: 14.2216, // Mock coordinates for Ibb
        longitude: 44.1814,
        address: _pickupLocationController.text.trim(),
      ),
      dropoffLocation: _needsDropoffLocation && _dropoffLocationController.text.isNotEmpty
          ? LocationModel(
              latitude: 14.2316, // Mock coordinates
              longitude: 44.1914,
              address: _dropoffLocationController.text.trim(),
            )
          : null,
      estimatedPrice: _calculateEstimatedPrice(),
      status: RequestStatus.pending,
      paymentMethod: _selectedPaymentMethod,
      createdAt: DateTime.now(),
    );

    appState.setLoading(true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    
    appState.addActiveRequest(request);
    appState.setLoading(false);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إرسال طلبك بنجاح! ننتظر موافقة مقدم الخدمة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: AppColors.success,
        ),
      );
      
      Navigator.of(context).pop();
    }
  }

  double _calculateEstimatedPrice() {
    // Mock price calculation based on service type
    switch (_selectedServiceType) {
      case ServiceType.driver:
        return 15.0;
      case ServiceType.electrician:
        return 50.0;
      case ServiceType.plumber:
        return 40.0;
      case ServiceType.carpenter:
        return 60.0;
      case ServiceType.mechanic:
        return 80.0;
      case ServiceType.cleaner:
        return 30.0;
      case ServiceType.painter:
        return 70.0;
      case ServiceType.gardener:
        return 35.0;
      default:
        return 25.0;
    }
  }
}
