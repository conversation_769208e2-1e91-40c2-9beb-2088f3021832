import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../models/service_request_model.dart';
import '../../models/user_model.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/custom_button.dart';
import '../chat/chat_screen.dart';

class RequestDetailsScreen extends StatefulWidget {
  final ServiceRequestModel request;
  final UserModel? serviceProvider;

  const RequestDetailsScreen({
    super.key,
    required this.request,
    this.serviceProvider,
  });

  @override
  State<RequestDetailsScreen> createState() => _RequestDetailsScreenState();
}

class _RequestDetailsScreenState extends State<RequestDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Request Status Card
            _buildStatusCard(),

            SizedBox(height: 24.h),

            // Service Details
            _buildServiceDetails(),

            SizedBox(height: 24.h),

            // Location Details
            _buildLocationDetails(),

            SizedBox(height: 24.h),

            // Price Details
            _buildPriceDetails(),

            if (widget.serviceProvider != null) ...[
              SizedBox(height: 24.h),

              // Service Provider Info
              _buildServiceProviderInfo(),
            ],

            SizedBox(height: 24.h),

            // Payment Method
            _buildPaymentMethod(),

            SizedBox(height: 32.h),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      title: Text(
        AppStrings.requestDetails,
        style: GoogleFonts.cairo(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        if (widget.serviceProvider != null)
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChatScreen(
                    otherUser: widget.serviceProvider!,
                    requestId: widget.request.id,
                  ),
                ),
              );
            },
            icon: Icon(
              Icons.chat_bubble_outline,
              size: 24.sp,
            ),
          ),
      ],
    );
  }

  Widget _buildStatusCard() {
    Color statusColor;
    IconData statusIcon;

    switch (widget.request.status) {
      case RequestStatus.pending:
        statusColor = AppColors.warning;
        statusIcon = Icons.schedule;
        break;
      case RequestStatus.accepted:
        statusColor = AppColors.info;
        statusIcon = Icons.check_circle_outline;
        break;
      case RequestStatus.inProgress:
        statusColor = AppColors.primary;
        statusIcon = Icons.play_circle_outline;
        break;
      case RequestStatus.completed:
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        break;
      case RequestStatus.cancelled:
        statusColor = AppColors.error;
        statusIcon = Icons.cancel;
        break;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 32.sp,
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.request.status.displayName,
                  style: GoogleFonts.cairo(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getStatusDescription(),
                  style: GoogleFonts.cairo(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceDetails() {
    return _buildSection(
      title: 'تفاصيل الخدمة',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            label: AppStrings.serviceType,
            value: widget.request.serviceType.displayName,
            icon: Icons.work_outline,
          ),
          SizedBox(height: 16.h),
          _buildDetailRow(
            label: 'العنوان',
            value: widget.request.title,
            icon: Icons.title,
          ),
          SizedBox(height: 16.h),
          _buildDetailRow(
            label: AppStrings.description,
            value: widget.request.description,
            icon: Icons.description,
            isMultiline: true,
          ),
        ],
      ),
    );
  }

  Widget _buildLocationDetails() {
    return _buildSection(
      title: 'تفاصيل الموقع',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            label: AppStrings.pickupLocation,
            value: widget.request.pickupLocation.address,
            icon: Icons.location_on,
          ),
          if (widget.request.dropoffLocation != null) ...[
            SizedBox(height: 16.h),
            _buildDetailRow(
              label: AppStrings.dropoffLocation,
              value: widget.request.dropoffLocation!.address,
              icon: Icons.flag,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPriceDetails() {
    return _buildSection(
      title: 'تفاصيل السعر',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.request.estimatedPrice != null)
            _buildDetailRow(
              label: AppStrings.estimatedPrice,
              value: '${widget.request.estimatedPrice!.toStringAsFixed(0)} ر.س',
              icon: Icons.attach_money,
            ),
          if (widget.request.finalPrice != null) ...[
            if (widget.request.estimatedPrice != null) SizedBox(height: 16.h),
            _buildDetailRow(
              label: AppStrings.finalPrice,
              value: '${widget.request.finalPrice!.toStringAsFixed(0)} ر.س',
              icon: Icons.payment,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildServiceProviderInfo() {
    return _buildSection(
      title: 'معلومات مقدم الخدمة',
      child: Row(
        children: [
          CircleAvatar(
            radius: 30.r,
            backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            child: widget.serviceProvider!.profileImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(30.r),
                    child: Image.network(
                      widget.serviceProvider!.profileImage!,
                      width: 60.w,
                      height: 60.w,
                      fit: BoxFit.cover,
                    ),
                  )
                : Text(
                    widget.serviceProvider!.serviceType?.icon ?? '👤',
                    style: TextStyle(fontSize: 24.sp),
                  ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.serviceProvider!.fullName,
                  style: GoogleFonts.cairo(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  widget.serviceProvider!.serviceType?.displayName ?? '',
                  style: GoogleFonts.cairo(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                if (widget.serviceProvider!.rating != null) ...[
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.accent,
                        size: 16.sp,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        widget.serviceProvider!.rating!.toStringAsFixed(1),
                        style: GoogleFonts.cairo(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethod() {
    return _buildSection(
      title: AppStrings.paymentMethod,
      child: _buildDetailRow(
        label: 'الطريقة المختارة',
        value: widget.request.paymentMethod.displayName,
        icon: widget.request.paymentMethod == PaymentMethod.cash
            ? Icons.money
            : Icons.credit_card,
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  Widget _buildDetailRow({
    required String label,
    required String value,
    required IconData icon,
    bool isMultiline = false,
  }) {
    return Row(
      crossAxisAlignment:
          isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 20.sp,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 14.sp,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        if (widget.request.status == RequestStatus.pending) {
          return CustomButton(
            text: AppStrings.cancelRequest,
            backgroundColor: AppColors.error,
            onPressed: () => _cancelRequest(),
          );
        } else if (widget.request.status == RequestStatus.completed) {
          return Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'تقييم الخدمة',
                  outlined: true,
                  onPressed: () {},
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CustomButton(
                  text: 'طلب مرة أخرى',
                  onPressed: () {},
                ),
              ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  String _getStatusDescription() {
    switch (widget.request.status) {
      case RequestStatus.pending:
        return 'في انتظار موافقة مقدم الخدمة';
      case RequestStatus.accepted:
        return 'تم قبول طلبك، مقدم الخدمة في الطريق';
      case RequestStatus.inProgress:
        return 'جاري تنفيذ الخدمة';
      case RequestStatus.completed:
        return 'تم إكمال الخدمة بنجاح';
      case RequestStatus.cancelled:
        return 'تم إلغاء الطلب';
    }
  }

  void _cancelRequest() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إلغاء الطلب',
          style: GoogleFonts.cairo(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟',
          style: GoogleFonts.cairo(fontSize: 16.sp),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppStrings.cancel,
              style: GoogleFonts.cairo(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppStateProvider>().updateRequestStatus(
                    widget.request.id,
                    RequestStatus.cancelled,
                  );
              Navigator.of(context).pop();
            },
            child: Text(
              'إلغاء الطلب',
              style: GoogleFonts.cairo(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
