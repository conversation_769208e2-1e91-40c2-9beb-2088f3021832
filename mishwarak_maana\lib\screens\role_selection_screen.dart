import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../constants/icon_colors.dart';
import '../models/user_model.dart';
import '../providers/app_state_provider.dart';
import '../utils/responsive_helper.dart';
import '../widgets/responsive_widgets.dart';
import '../widgets/enhanced_icon.dart';
import 'auth/login_screen.dart';

class RoleSelectionScreen extends StatelessWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.surface,
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: ResponsiveHelper.responsiveBuilder(
            mobile: _buildMobileLayout(context),
            tablet: _buildTabletLayout(context),
            desktop: _buildDesktopLayout(context),
          ),
        ),
      ),
    );
  }

  // Mobile Layout (Portrait)
  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: ResponsiveHelper.screenPadding,
      child: Column(
        children: [
          ResponsiveSpacing.medium(),
          _buildHeader(),
          ResponsiveSpacing.large(),
          _buildRoleCard(
            context,
            role: UserRole.client,
            title: AppStrings.client,
            description: AppStrings.clientDescription,
            icon: Icons.person,
            color: AppColors.primary,
          ),
          ResponsiveSpacing.medium(),
          _buildRoleCard(
            context,
            role: UserRole.serviceProvider,
            title: AppStrings.serviceProvider,
            description: AppStrings.serviceProviderDescription,
            icon: Icons.work,
            color: AppColors.secondary,
          ),
          ResponsiveSpacing.extraLarge(),
        ],
      ),
    );
  }

  // Tablet Layout
  Widget _buildTabletLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: ResponsiveHelper.screenPadding,
      child: Column(
        children: [
          ResponsiveSpacing.large(),
          _buildHeader(),
          ResponsiveSpacing.extraLarge(),
          // Cards in a row for tablet
          Row(
            children: [
              Expanded(
                child: _buildRoleCard(
                  context,
                  role: UserRole.client,
                  title: AppStrings.client,
                  description: AppStrings.clientDescription,
                  icon: Icons.person,
                  color: AppColors.primary,
                ),
              ),
              ResponsiveSpacing.medium(isHorizontal: true),
              Expanded(
                child: _buildRoleCard(
                  context,
                  role: UserRole.serviceProvider,
                  title: AppStrings.serviceProvider,
                  description: AppStrings.serviceProviderDescription,
                  icon: Icons.work,
                  color: AppColors.secondary,
                ),
              ),
            ],
          ),
          ResponsiveSpacing.extraLarge(),
        ],
      ),
    );
  }

  // Desktop Layout
  Widget _buildDesktopLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: ResponsiveHelper.maxContentWidth),
        padding: ResponsiveHelper.screenPadding,
        child: SingleChildScrollView(
          child: Column(
            children: [
              ResponsiveSpacing.extraLarge(),
              _buildHeader(),
              ResponsiveSpacing.extraLarge(),
              // Cards in a centered row for desktop
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 400,
                    child: _buildRoleCard(
                      context,
                      role: UserRole.client,
                      title: AppStrings.client,
                      description: AppStrings.clientDescription,
                      icon: Icons.person,
                      color: AppColors.primary,
                    ),
                  ),
                  ResponsiveSpacing.extraLarge(isHorizontal: true),
                  SizedBox(
                    width: 400,
                    child: _buildRoleCard(
                      context,
                      role: UserRole.serviceProvider,
                      title: AppStrings.serviceProvider,
                      description: AppStrings.serviceProviderDescription,
                      icon: Icons.work,
                      color: AppColors.secondary,
                    ),
                  ),
                ],
              ),
              ResponsiveSpacing.extraLarge(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App Logo
        ResponsiveContainer(
          width: ResponsiveHelper.getResponsiveValue(
            mobile: 60.w,
            tablet: 70.w,
            desktop: 80.w,
          ),
          height: ResponsiveHelper.getResponsiveValue(
            mobile: 60.w,
            tablet: 70.w,
            desktop: 80.w,
          ),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(ResponsiveHelper.largeRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: ResponsiveHelper.isMobile ? 10.r : 15.r,
                offset: Offset(0, ResponsiveHelper.isMobile ? 5.h : 8.h),
              ),
            ],
          ),
          child: Icon(
            Icons.miscellaneous_services_rounded,
            size: ResponsiveHelper.largeIconSize,
            color: AppColors.textOnPrimary,
          ),
        ),

        ResponsiveSpacing.medium(),

        // Title
        ResponsiveText(
          AppStrings.selectRole,
          type: ResponsiveTextType.title,
          textAlign: TextAlign.center,
        ),

        ResponsiveSpacing.small(),

        // Subtitle
        ResponsiveText(
          'اختر الدور المناسب لك للمتابعة',
          type: ResponsiveTextType.body,
          style: TextStyle(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRoleCard(
    BuildContext context, {
    required UserRole role,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: ResponsiveCard(
          onTap: () => _selectRole(context, role),
          padding: ResponsiveHelper.cardPadding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Enhanced Icon with modern design
              EnhancedIcon(
                icon: icon,
                color: Colors.white,
                size: ResponsiveHelper.getResponsiveValue(
                  mobile: 30,
                  tablet: 35,
                  desktop: 40,
                ),
                backgroundColor: color,
                backgroundSize: ResponsiveHelper.getResponsiveValue(
                  mobile: 60,
                  tablet: 70,
                  desktop: 80,
                ),
                borderRadius:
                    BorderRadius.circular(ResponsiveHelper.largeRadius),
                borderWidth: 2,
                borderColor: color.withValues(alpha: 0.3),
                hasGradient: false,
                hasShadow: true,
                hasGlow: true,
                isAnimated: true,
                customGradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color,
                    IconColors.getLightVariant(color),
                  ],
                ),
                customShadows: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: color.withValues(alpha: 0.1),
                    blurRadius: 25,
                    offset: const Offset(0, 10),
                    spreadRadius: 5,
                  ),
                ],
              ),

              ResponsiveSpacing.large(),

              // Title with enhanced styling
              ResponsiveText(
                title,
                type: ResponsiveTextType.heading,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),

              ResponsiveSpacing.medium(),

              // Description with better styling
              ResponsiveText(
                description,
                type: ResponsiveTextType.body,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              ResponsiveSpacing.large(),

              // Enhanced action indicator
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: ResponsiveHelper.mediumSpacing,
                  vertical: ResponsiveHelper.smallSpacing,
                ),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(ResponsiveHelper.smallRadius),
                  border: Border.all(
                    color: color.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: ResponsiveText(
                        'اختر',
                        type: ResponsiveTextType.caption,
                        style: TextStyle(
                          color: color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(width: ResponsiveHelper.smallSpacing / 2),
                    EnhancedIcon(
                      icon: Icons.arrow_forward_ios,
                      color: color,
                      size: ResponsiveHelper.smallIconSize,
                      isAnimated: true,
                      hasGlow: false,
                      hasShadow: false,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectRole(BuildContext context, UserRole role) {
    // Set selected role in provider
    context.read<AppStateProvider>().setSelectedRole(role);

    // Navigate to login screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    );
  }
}
