import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import 'role_selection_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _navigateToNextScreen();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  void _navigateToNextScreen() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const RoleSelectionScreen(),
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradient,
      ),
      child: SafeArea(
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // App Logo with enhanced design for "خدمتي بلاس"
                          Container(
                            width: 120.w,
                            height: 120.w,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.primary.withValues(alpha: 0.1),
                                  AppColors.secondary.withValues(alpha: 0.1),
                                  AppColors.surface,
                                ],
                                stops: [0.0, 0.5, 1.0],
                              ),
                              borderRadius: BorderRadius.circular(30.r),
                              border: Border.all(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                width: 4,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.shadowMedium,
                                  blurRadius: 30.r,
                                  offset: Offset(0, 15.h),
                                ),
                                BoxShadow(
                                  color:
                                      AppColors.primary.withValues(alpha: 0.2),
                                  blurRadius: 20.r,
                                  offset: Offset(0, 8.h),
                                ),
                                BoxShadow(
                                  color: AppColors.secondary
                                      .withValues(alpha: 0.1),
                                  blurRadius: 10.r,
                                  offset: Offset(0, 3.h),
                                ),
                              ],
                            ),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Background pattern
                                Container(
                                  width: 90.w,
                                  height: 90.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        AppColors.primary
                                            .withValues(alpha: 0.1),
                                        AppColors.secondary
                                            .withValues(alpha: 0.1),
                                      ],
                                    ),
                                  ),
                                ),
                                // Enhanced main service icon - Modern Design
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // Background circle with gradient
                                    Container(
                                      width: 70.w,
                                      height: 70.w,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            AppColors.primary
                                                .withValues(alpha: 0.2),
                                            AppColors.secondary
                                                .withValues(alpha: 0.2),
                                          ],
                                        ),
                                      ),
                                    ),
                                    // Main service icon
                                    Icon(
                                      Icons.miscellaneous_services_rounded,
                                      size: 45.sp,
                                      color: AppColors.primary,
                                    ),
                                    // Small overlay icons for services
                                    Positioned(
                                      top: 8.h,
                                      right: 8.w,
                                      child: Container(
                                        width: 16.w,
                                        height: 16.w,
                                        decoration: BoxDecoration(
                                          color: AppColors.secondary,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: AppColors.surface,
                                            width: 1,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.build_rounded,
                                          size: 10.sp,
                                          color: AppColors.textOnPrimary,
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 8.h,
                                      left: 8.w,
                                      child: Container(
                                        width: 16.w,
                                        height: 16.w,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: AppColors.surface,
                                            width: 1,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.home_repair_service_rounded,
                                          size: 10.sp,
                                          color: AppColors.textOnPrimary,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                // Plus symbol overlay
                                Positioned(
                                  bottom: 15.h,
                                  right: 15.w,
                                  child: Container(
                                    width: 24.w,
                                    height: 24.w,
                                    decoration: BoxDecoration(
                                      color: AppColors.secondary,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: AppColors.surface,
                                        width: 2,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      size: 16.sp,
                                      color: AppColors.textOnPrimary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 30.h),

                          // App Name with enhanced styling
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 24.w,
                              vertical: 12.h,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  AppColors.primary.withValues(alpha: 0.1),
                                  AppColors.secondary.withValues(alpha: 0.1),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25.r),
                              border: Border.all(
                                color: AppColors.primary.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              AppStrings.appName,
                              style: GoogleFonts.cairo(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textOnPrimary,
                                letterSpacing: 1.2,
                              ),
                            ),
                          ),

                          SizedBox(height: 16.h),

                          // App Slogan with enhanced styling
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20.w,
                              vertical: 8.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.surface.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20.r),
                            ),
                            child: Text(
                              AppStrings.appSlogan,
                              style: GoogleFonts.cairo(
                                fontSize: 14.sp,
                                color: AppColors.textOnPrimary
                                    .withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          SizedBox(height: 20.h),

                          // Service icons preview
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildSimpleServiceIcon(
                                  Icons.directions_car, AppColors.primary),
                              SizedBox(width: 12.w),
                              _buildSimpleServiceIcon(Icons.electrical_services,
                                  AppColors.secondary),
                              SizedBox(width: 12.w),
                              _buildSimpleServiceIcon(
                                  Icons.plumbing, AppColors.primary),
                              SizedBox(width: 12.w),
                              _buildSimpleServiceIcon(
                                  Icons.build, AppColors.secondary),
                            ],
                          ),

                          SizedBox(height: 40.h),

                          // Loading Indicator
                          SizedBox(
                            width: 30.w,
                            height: 30.w,
                            child: const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.surface,
                              ),
                              strokeWidth: 3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ));
            },
          ),
        ),
      ),
    ));
  }

  Widget _buildSimpleServiceIcon(IconData icon, Color color) {
    return Container(
      width: 32.w,
      height: 32.w,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.surface,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        icon,
        size: 16.sp,
        color: AppColors.textOnPrimary,
      ),
    );
  }
}
