class AppConfig {
  // App Information
  static const String appName = 'مشوارك معنا';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // API Configuration
  static const String baseUrl = 'https://api.mishwarak-maana.com';
  static const String apiVersion = 'v1';
  static const String apiKey = 'your-api-key-here';
  
  // Map Configuration
  static const String googleMapsApiKey = 'your-google-maps-api-key';
  static const double defaultLatitude = 14.2216; // Ibb, Yemen
  static const double defaultLongitude = 44.1814;
  
  // App Settings
  static const int requestTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  static const bool enableLogging = true;
  
  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  
  // Notification Settings
  static const String fcmServerKey = 'your-fcm-server-key';
  static const String notificationChannelId = 'mishwarak_notifications';
  static const String notificationChannelName = 'Mishwarak Notifications';
  
  // Payment Configuration
  static const bool enableElectronicPayment = false; // Will be enabled later
  static const List<String> supportedPaymentMethods = ['cash'];
  
  // Service Types Configuration
  static const Map<String, Map<String, dynamic>> serviceTypeConfig = {
    'driver': {
      'basePrice': 15.0,
      'pricePerKm': 2.0,
      'minimumPrice': 10.0,
      'icon': '🚗',
      'color': 0xFF2196F3,
    },
    'electrician': {
      'basePrice': 50.0,
      'pricePerHour': 25.0,
      'minimumPrice': 30.0,
      'icon': '⚡',
      'color': 0xFFFFEB3B,
    },
    'plumber': {
      'basePrice': 40.0,
      'pricePerHour': 20.0,
      'minimumPrice': 25.0,
      'icon': '🔧',
      'color': 0xFF00BCD4,
    },
    'carpenter': {
      'basePrice': 60.0,
      'pricePerHour': 30.0,
      'minimumPrice': 40.0,
      'icon': '🔨',
      'color': 0xFF8BC34A,
    },
    'mechanic': {
      'basePrice': 80.0,
      'pricePerHour': 40.0,
      'minimumPrice': 50.0,
      'icon': '🔧',
      'color': 0xFF9C27B0,
    },
    'cleaner': {
      'basePrice': 30.0,
      'pricePerHour': 15.0,
      'minimumPrice': 20.0,
      'icon': '🧹',
      'color': 0xFF00E676,
    },
    'painter': {
      'basePrice': 70.0,
      'pricePerHour': 35.0,
      'minimumPrice': 45.0,
      'icon': '🎨',
      'color': 0xFFE91E63,
    },
    'gardener': {
      'basePrice': 35.0,
      'pricePerHour': 18.0,
      'minimumPrice': 25.0,
      'icon': '🌱',
      'color': 0xFF4CAF50,
    },
  };
  
  // App Features Flags
  static const bool enableChat = true;
  static const bool enableNotifications = true;
  static const bool enableLocationTracking = true;
  static const bool enableRatings = true;
  static const bool enableOfflineMode = false; // Future feature
  
  // Regional Settings
  static const String defaultCountryCode = '+967';
  static const String defaultCurrency = 'YER';
  static const String defaultCurrencySymbol = 'ر.س';
  static const String defaultLanguage = 'ar';
  
  // Business Hours
  static const Map<String, String> businessHours = {
    'start': '06:00',
    'end': '23:00',
  };
  
  // Support Information
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+967777777777';
  static const String websiteUrl = 'https://www.mishwarak-maana.com';
  static const String privacyPolicyUrl = 'https://www.mishwarak-maana.com/privacy';
  static const String termsOfServiceUrl = 'https://www.mishwarak-maana.com/terms';
  
  // Social Media Links
  static const Map<String, String> socialMediaLinks = {
    'facebook': 'https://facebook.com/mishwarak.maana',
    'twitter': 'https://twitter.com/mishwarak_maana',
    'instagram': 'https://instagram.com/mishwarak.maana',
    'whatsapp': 'https://wa.me/967777777777',
  };
  
  // Error Messages
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال بالإنترنت',
    'server_error': 'خطأ في الخادم، يرجى المحاولة لاحقاً',
    'timeout_error': 'انتهت مهلة الاتصال',
    'unknown_error': 'حدث خطأ غير متوقع',
    'validation_error': 'يرجى التحقق من البيانات المدخلة',
    'permission_denied': 'ليس لديك صلاحية للوصول',
    'location_permission_denied': 'يرجى السماح بالوصول للموقع',
    'camera_permission_denied': 'يرجى السماح بالوصول للكاميرا',
  };
  
  // Success Messages
  static const Map<String, String> successMessages = {
    'request_sent': 'تم إرسال طلبك بنجاح',
    'request_accepted': 'تم قبول طلبك',
    'request_completed': 'تم إكمال الخدمة بنجاح',
    'profile_updated': 'تم تحديث الملف الشخصي',
    'password_changed': 'تم تغيير كلمة المرور',
    'message_sent': 'تم إرسال الرسالة',
  };
  
  // Validation Rules
  static const Map<String, dynamic> validationRules = {
    'password_min_length': 6,
    'phone_min_length': 9,
    'phone_max_length': 15,
    'name_min_length': 2,
    'name_max_length': 50,
    'description_max_length': 500,
    'title_max_length': 100,
  };
  
  // Cache Settings
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB
  
  // Image Settings
  static const int maxImageSize = 5; // MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png'];
  static const int imageQuality = 80; // 0-100
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Rating System
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double defaultRating = 0.0;
  
  // Distance Settings
  static const double maxServiceRadius = 50.0; // km
  static const double defaultSearchRadius = 10.0; // km
  
  // Environment Check
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get isDevelopment => !isProduction;
  
  // Get API endpoint
  static String getApiEndpoint(String endpoint) {
    return '$baseUrl/$apiVersion/$endpoint';
  }
  
  // Get service type configuration
  static Map<String, dynamic>? getServiceTypeConfig(String serviceType) {
    return serviceTypeConfig[serviceType];
  }
  
  // Get service type price
  static double getServiceTypeBasePrice(String serviceType) {
    final config = getServiceTypeConfig(serviceType);
    return config?['basePrice']?.toDouble() ?? 25.0;
  }
  
  // Check if feature is enabled
  static bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'chat':
        return enableChat;
      case 'notifications':
        return enableNotifications;
      case 'location_tracking':
        return enableLocationTracking;
      case 'ratings':
        return enableRatings;
      case 'offline_mode':
        return enableOfflineMode;
      case 'electronic_payment':
        return enableElectronicPayment;
      default:
        return false;
    }
  }
}
