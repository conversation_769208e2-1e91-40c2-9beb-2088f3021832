import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_colors.dart';
import '../models/user_model.dart';
import '../models/service_request_model.dart';

class Helpers {
  // Date and Time Formatting
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }

  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  static String formatDate(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  static String formatDateTimeDetailed(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy - HH:mm').format(dateTime);
  }

  // Price Formatting
  static String formatPrice(double price) {
    return '${price.toStringAsFixed(0)} ر.س';
  }

  static String formatPriceWithDecimals(double price) {
    return '${price.toStringAsFixed(2)} ر.س';
  }

  // Phone Number Formatting
  static String formatPhoneNumber(String phone) {
    // Remove any non-digit characters
    String cleaned = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Add country code if not present
    if (!cleaned.startsWith('967')) {
      if (cleaned.startsWith('0')) {
        cleaned = '967${cleaned.substring(1)}';
      } else {
        cleaned = '967$cleaned';
      }
    }

    // Format as +967 XXX XXX XXX
    if (cleaned.length >= 12) {
      return '+${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6, 9)} ${cleaned.substring(9)}';
    }

    return '+$cleaned';
  }

  // Validation Helpers
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool isValidPhone(String phone) {
    String cleaned = phone.replaceAll(RegExp(r'[^\d]'), '');
    return cleaned.length >= 9 && cleaned.length <= 15;
  }

  static bool isValidPassword(String password) {
    return password.length >= 6;
  }

  // Distance Calculation
  static double calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    // Simple distance calculation (not accurate for long distances)
    // For production, use a proper geolocation library
    const double earthRadius = 6371; // km

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  static String formatDistance(double distanceKm) {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()} م';
    } else {
      return '${distanceKm.toStringAsFixed(1)} كم';
    }
  }

  // Color Helpers
  static Color getServiceTypeColor(ServiceType serviceType) {
    switch (serviceType) {
      case ServiceType.driver:
        return AppColors.driverColor;
      case ServiceType.electrician:
        return AppColors.electricianColor;
      case ServiceType.plumber:
        return AppColors.plumberColor;
      case ServiceType.carpenter:
        return AppColors.carpenterColor;
      case ServiceType.mechanic:
        return AppColors.mechanicColor;
      case ServiceType.cleaner:
        return AppColors.cleanerColor;
      case ServiceType.painter:
        return AppColors.painterColor;
      case ServiceType.gardener:
        return AppColors.gardenerColor;
    }
  }

  static Color getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return AppColors.warning;
      case RequestStatus.accepted:
        return AppColors.info;
      case RequestStatus.inProgress:
        return AppColors.primary;
      case RequestStatus.completed:
        return AppColors.success;
      case RequestStatus.cancelled:
        return AppColors.error;
    }
  }

  // String Helpers
  static String truncateString(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }

  static String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  static String removeExtraSpaces(String text) {
    return text.replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  // Rating Helpers
  static String formatRating(double rating) {
    return rating.toStringAsFixed(1);
  }

  static List<Widget> buildStarRating(double rating, {double size = 16}) {
    List<Widget> stars = [];
    int fullStars = rating.floor();
    bool hasHalfStar = (rating - fullStars) >= 0.5;

    // Full stars
    for (int i = 0; i < fullStars; i++) {
      stars.add(Icon(
        Icons.star,
        color: AppColors.accent,
        size: size,
      ));
    }

    // Half star
    if (hasHalfStar) {
      stars.add(Icon(
        Icons.star_half,
        color: AppColors.accent,
        size: size,
      ));
    }

    // Empty stars
    int emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (int i = 0; i < emptyStars; i++) {
      stars.add(Icon(
        Icons.star_border,
        color: AppColors.textSecondary,
        size: size,
      ));
    }

    return stars;
  }

  // Network Helpers
  static bool isNetworkError(dynamic error) {
    return error.toString().contains('SocketException') ||
        error.toString().contains('TimeoutException') ||
        error.toString().contains('HandshakeException');
  }

  static String getErrorMessage(dynamic error) {
    if (isNetworkError(error)) {
      return 'خطأ في الاتصال بالإنترنت';
    }

    // Add more specific error handling here
    return 'حدث خطأ غير متوقع';
  }

  // UI Helpers
  static void showSnackBar(BuildContext context, String message,
      {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  // Device Helpers
  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.shortestSide >= 600;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // Storage Helpers
  static String generateUniqueId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Price Calculation Helpers
  static double calculateServicePrice({
    required ServiceType serviceType,
    double? distance,
    double? duration,
  }) {
    // Base prices for different services
    Map<ServiceType, double> basePrices = {
      ServiceType.driver: 15.0,
      ServiceType.electrician: 50.0,
      ServiceType.plumber: 40.0,
      ServiceType.carpenter: 60.0,
      ServiceType.mechanic: 80.0,
      ServiceType.cleaner: 30.0,
      ServiceType.painter: 70.0,
      ServiceType.gardener: 35.0,
    };

    double basePrice = basePrices[serviceType] ?? 25.0;

    // Add distance-based pricing for drivers
    if (serviceType == ServiceType.driver && distance != null) {
      basePrice += distance * 2.0; // 2 YER per km
    }

    // Add duration-based pricing for other services
    if (serviceType != ServiceType.driver && duration != null) {
      double hourlyRate = basePrice * 0.5; // 50% of base price per hour
      basePrice += (duration / 60) * hourlyRate;
    }

    return basePrice;
  }

  // Text Direction Helper
  static bool isArabicText(String text) {
    // Simple Arabic text detection
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }
}
