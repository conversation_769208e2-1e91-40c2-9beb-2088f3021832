import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResponsiveHelper {
  static late BuildContext _context;

  static void init(BuildContext context) {
    _context = context;
  }

  // Screen size getters
  static double get screenWidth => MediaQuery.of(_context).size.width;
  static double get screenHeight => MediaQuery.of(_context).size.height;
  static double get statusBarHeight => MediaQuery.of(_context).padding.top;
  static double get bottomPadding => MediaQuery.of(_context).padding.bottom;

  // Device type detection
  static bool get isMobile => screenWidth < 600;
  static bool get isTablet => screenWidth >= 600 && screenWidth < 1200;
  static bool get isDesktop => screenWidth >= 1200;
  static bool get isLandscape => screenWidth > screenHeight;
  static bool get isPortrait => screenHeight > screenWidth;

  // Responsive dimensions
  static double responsiveWidth(double percentage) {
    return screenWidth * (percentage / 100);
  }

  static double responsiveHeight(double percentage) {
    return screenHeight * (percentage / 100);
  }

  // Responsive font sizes
  static double get titleFontSize {
    if (isMobile) return 18.sp;
    if (isTablet) return 20.sp;
    return 22.sp;
  }

  static double get headingFontSize {
    if (isMobile) return 16.sp;
    if (isTablet) return 18.sp;
    return 20.sp;
  }

  static double get bodyFontSize {
    if (isMobile) return 14.sp;
    if (isTablet) return 15.sp;
    return 16.sp;
  }

  static double get captionFontSize {
    if (isMobile) return 12.sp;
    if (isTablet) return 13.sp;
    return 14.sp;
  }

  // Responsive spacing
  static double get smallSpacing {
    if (isMobile) return 8.h;
    if (isTablet) return 12.h;
    return 16.h;
  }

  static double get mediumSpacing {
    if (isMobile) return 16.h;
    if (isTablet) return 20.h;
    return 24.h;
  }

  static double get largeSpacing {
    if (isMobile) return 24.h;
    if (isTablet) return 32.h;
    return 40.h;
  }

  static double get extraLargeSpacing {
    if (isMobile) return 32.h;
    if (isTablet) return 48.h;
    return 64.h;
  }

  // Responsive padding
  static EdgeInsets get screenPadding {
    if (isMobile) return EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h);
    if (isTablet) return EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h);
    return EdgeInsets.symmetric(horizontal: 48.w, vertical: 32.h);
  }

  static EdgeInsets get cardPadding {
    if (isMobile) return EdgeInsets.all(16.w);
    if (isTablet) return EdgeInsets.all(20.w);
    return EdgeInsets.all(24.w);
  }

  // Responsive border radius
  static double get smallRadius {
    if (isMobile) return 8.r;
    if (isTablet) return 10.r;
    return 12.r;
  }

  static double get mediumRadius {
    if (isMobile) return 12.r;
    if (isTablet) return 16.r;
    return 20.r;
  }

  static double get largeRadius {
    if (isMobile) return 16.r;
    if (isTablet) return 20.r;
    return 24.r;
  }

  // Responsive icon sizes
  static double get smallIconSize {
    if (isMobile) return 16.sp;
    if (isTablet) return 18.sp;
    return 20.sp;
  }

  static double get mediumIconSize {
    if (isMobile) return 20.sp;
    if (isTablet) return 22.sp;
    return 24.sp;
  }

  static double get largeIconSize {
    if (isMobile) return 24.sp;
    if (isTablet) return 28.sp;
    return 32.sp;
  }

  // Responsive button dimensions
  static double get buttonHeight {
    if (isMobile) return 48.h;
    if (isTablet) return 52.h;
    return 56.h;
  }

  static double get smallButtonHeight {
    if (isMobile) return 36.h;
    if (isTablet) return 40.h;
    return 44.h;
  }

  // Grid columns for different screen sizes
  static int get gridColumns {
    if (isMobile && isPortrait) return 2;
    if (isMobile && isLandscape) return 3;
    if (isTablet && isPortrait) return 3;
    if (isTablet && isLandscape) return 4;
    return 5; // Desktop
  }

  // Maximum content width for large screens
  static double get maxContentWidth {
    if (isDesktop) return 1200.w;
    return double.infinity;
  }

  // Responsive card dimensions
  static double get cardMinHeight {
    if (isMobile) return 120.h;
    if (isTablet) return 140.h;
    return 160.h;
  }

  static double get cardMaxWidth {
    if (isMobile) return double.infinity;
    if (isTablet) return 400.w;
    return 500.w;
  }

  // Responsive app bar height
  static double get appBarHeight {
    if (isMobile) return kToolbarHeight;
    if (isTablet) return kToolbarHeight + 8;
    return kToolbarHeight + 16;
  }

  // Responsive bottom navigation height
  static double get bottomNavHeight {
    if (isMobile) return 60.h;
    if (isTablet) return 70.h;
    return 80.h;
  }

  // Responsive dialog width
  static double get dialogWidth {
    if (isMobile) return screenWidth * 0.9;
    if (isTablet) return screenWidth * 0.7;
    return screenWidth * 0.5;
  }

  // Responsive list tile height
  static double get listTileHeight {
    if (isMobile) return 72.h;
    if (isTablet) return 80.h;
    return 88.h;
  }

  // Safe area padding
  static EdgeInsets get safeAreaPadding {
    return EdgeInsets.only(
      top: statusBarHeight,
      bottom: bottomPadding,
    );
  }

  // Responsive breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1200;

  // Helper method to get responsive value based on screen size
  static T getResponsiveValue<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop && desktop != null) return desktop;
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }

  // Responsive layout builder
  static Widget responsiveBuilder({
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint && desktop != null) {
          return desktop;
        } else if (constraints.maxWidth >= mobileBreakpoint && tablet != null) {
          return tablet;
        } else {
          return mobile;
        }
      },
    );
  }
}
