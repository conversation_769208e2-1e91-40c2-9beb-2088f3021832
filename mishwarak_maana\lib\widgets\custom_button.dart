import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../utils/responsive_helper.dart';
import 'responsive_widgets.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final IconData? icon;
  final bool outlined;
  final double borderRadius;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.icon,
    this.outlined = false,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor =
        backgroundColor ?? (outlined ? Colors.transparent : AppColors.primary);
    final effectiveTextColor =
        textColor ?? (outlined ? AppColors.primary : AppColors.textOnPrimary);

    return SizedBox(
      width: width ?? double.infinity,
      height: height ??
          ResponsiveHelper.getResponsiveValue(
            mobile: 44,
            tablet: 48,
            desktop: 52,
          ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: effectiveBackgroundColor,
          foregroundColor: effectiveTextColor,
          elevation: outlined ? 0 : 2,
          shadowColor: AppColors.shadowLight,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.getResponsiveValue(
                mobile: borderRadius,
                tablet: borderRadius + 2,
                desktop: borderRadius + 4,
              ),
            ),
            side: outlined
                ? const BorderSide(color: AppColors.primary, width: 2)
                : BorderSide.none,
          ),
          disabledBackgroundColor: outlined
              ? Colors.transparent
              : AppColors.textSecondary.withValues(alpha: 0.3),
          disabledForegroundColor: AppColors.textSecondary,
        ),
        child: isLoading
            ? SizedBox(
                width: ResponsiveHelper.getResponsiveValue(
                  mobile: 20,
                  tablet: 22,
                  desktop: 24,
                ),
                height: ResponsiveHelper.getResponsiveValue(
                  mobile: 20,
                  tablet: 22,
                  desktop: 24,
                ),
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    outlined ? AppColors.primary : AppColors.textOnPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      size: ResponsiveHelper.getResponsiveValue(
                        mobile: 18,
                        tablet: 20,
                        desktop: 22,
                      ),
                      color: effectiveTextColor,
                    ),
                    SizedBox(width: ResponsiveHelper.smallSpacing),
                  ],
                  ResponsiveText(
                    text,
                    type: ResponsiveTextType.body,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: effectiveTextColor,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double iconSize;
  final String? tooltip;

  const CustomIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.iconSize = 24,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? '',
      child: ResponsiveContainer(
        width: ResponsiveHelper.getResponsiveValue(
          mobile: size,
          tablet: size + 4,
          desktop: size + 8,
        ),
        height: ResponsiveHelper.getResponsiveValue(
          mobile: size,
          tablet: size + 4,
          desktop: size + 8,
        ),
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.surface,
          borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: ResponsiveHelper.getResponsiveValue(
                mobile: 8,
                tablet: 10,
                desktop: 12,
              ),
              offset: Offset(
                  0,
                  ResponsiveHelper.getResponsiveValue(
                    mobile: 2,
                    tablet: 3,
                    desktop: 4,
                  )),
            ),
          ],
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            icon,
            size: ResponsiveHelper.getResponsiveValue(
              mobile: iconSize,
              tablet: iconSize + 2,
              desktop: iconSize + 4,
            ),
            color: iconColor ?? AppColors.textPrimary,
          ),
        ),
      ),
    );
  }
}
