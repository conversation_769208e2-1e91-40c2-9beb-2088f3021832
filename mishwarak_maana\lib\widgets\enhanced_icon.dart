import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/icon_colors.dart';
import '../utils/responsive_helper.dart';

/// أيقونة محسنة مع تأثيرات بصرية وتصميم جذاب
class EnhancedIcon extends StatefulWidget {
  final IconData icon;
  final Color? color;
  final double? size;
  final String? tooltip;
  final VoidCallback? onTap;
  final bool hasGradient;
  final bool hasShadow;
  final bool hasGlow;
  final bool isAnimated;
  final Color? backgroundColor;
  final double? backgroundSize;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? customShadows;
  final Gradient? customGradient;
  final Duration animationDuration;
  final bool isCircular;
  final double? borderWidth;
  final Color? borderColor;

  const EnhancedIcon({
    super.key,
    required this.icon,
    this.color,
    this.size,
    this.tooltip,
    this.onTap,
    this.hasGradient = false,
    this.hasShadow = true,
    this.hasGlow = false,
    this.isAnimated = true,
    this.backgroundColor,
    this.backgroundSize,
    this.borderRadius,
    this.customShadows,
    this.customGradient,
    this.animationDuration = const Duration(milliseconds: 200),
    this.isCircular = false,
    this.borderWidth,
    this.borderColor,
  });

  @override
  State<EnhancedIcon> createState() => _EnhancedIconState();
}

class _EnhancedIconState extends State<EnhancedIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (widget.isAnimated) {
      if (isHovered) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveColor =
        widget.color ?? IconColors.getPrimaryIconColor(isDark);
    final effectiveSize = widget.size ?? ResponsiveHelper.mediumIconSize;
    final effectiveBackgroundSize =
        widget.backgroundSize ?? effectiveSize * 1.8;

    Widget iconWidget = Icon(
      widget.icon,
      color: effectiveColor,
      size: effectiveSize,
    );

    // تطبيق التدرج إذا كان مطلوباً
    if (widget.hasGradient) {
      final gradient = widget.customGradient ?? IconColors.primaryGradient;

      iconWidget = ShaderMask(
        shaderCallback: (bounds) => gradient.createShader(bounds),
        child: Icon(
          widget.icon,
          color: Colors.white,
          size: effectiveSize,
        ),
      );
    }

    // إضافة الخلفية إذا كانت مطلوبة
    if (widget.backgroundColor != null) {
      iconWidget = Container(
        width: effectiveBackgroundSize,
        height: effectiveBackgroundSize,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: widget.isCircular
              ? BorderRadius.circular(effectiveBackgroundSize / 2)
              : widget.borderRadius ??
                  BorderRadius.circular(ResponsiveHelper.smallRadius),
          border: widget.borderWidth != null
              ? Border.all(
                  color: widget.borderColor ?? effectiveColor,
                  width: widget.borderWidth!,
                )
              : null,
          boxShadow: widget.hasShadow ? _buildShadows(effectiveColor) : null,
        ),
        child: Center(child: iconWidget),
      );
    }

    // إضافة تأثير التوهج
    if (widget.hasGlow) {
      iconWidget = AnimatedBuilder(
        animation: _glowAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: widget.isCircular
                  ? BorderRadius.circular(effectiveBackgroundSize / 2)
                  : widget.borderRadius ??
                      BorderRadius.circular(ResponsiveHelper.smallRadius),
              boxShadow: [
                BoxShadow(
                  color: effectiveColor.withValues(
                      alpha: 0.3 * _glowAnimation.value),
                  blurRadius: 20 * _glowAnimation.value,
                  spreadRadius: 5 * _glowAnimation.value,
                ),
              ],
            ),
            child: child,
          );
        },
        child: iconWidget,
      );
    }

    // إضافة الانيميشن
    if (widget.isAnimated) {
      iconWidget = AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: iconWidget,
      );
    }

    // إضافة التفاعل
    Widget interactiveWidget = MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: iconWidget,
      ),
    );

    // إضافة Tooltip إذا كان مطلوباً
    if (widget.tooltip != null) {
      interactiveWidget = Tooltip(
        message: widget.tooltip!,
        child: interactiveWidget,
      );
    }

    return interactiveWidget;
  }

  List<BoxShadow> _buildShadows(Color color) {
    if (widget.customShadows != null) {
      return widget.customShadows!;
    }

    return [
      BoxShadow(
        color: color.withValues(alpha: 0.2),
        blurRadius: ResponsiveHelper.getResponsiveValue(
          mobile: 8,
          tablet: 10,
          desktop: 12,
        ),
        offset: Offset(
            0,
            ResponsiveHelper.getResponsiveValue(
              mobile: 2,
              tablet: 3,
              desktop: 4,
            )),
      ),
      if (_isHovered)
        BoxShadow(
          color: color.withValues(alpha: 0.1),
          blurRadius: ResponsiveHelper.getResponsiveValue(
            mobile: 16,
            tablet: 20,
            desktop: 24,
          ),
          offset: Offset(
              0,
              ResponsiveHelper.getResponsiveValue(
                mobile: 4,
                tablet: 6,
                desktop: 8,
              )),
        ),
    ];
  }
}

/// أيقونة خدمة محسنة مع لون مخصص
class ServiceIcon extends StatelessWidget {
  final IconData icon;
  final String serviceType;
  final double? size;
  final bool hasBackground;
  final VoidCallback? onTap;

  const ServiceIcon({
    super.key,
    required this.icon,
    required this.serviceType,
    this.size,
    this.hasBackground = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = IconColors.getServiceColor(serviceType);

    return EnhancedIcon(
      icon: icon,
      color: hasBackground ? Colors.white : color,
      size: size,
      backgroundColor: hasBackground ? color : null,
      backgroundSize: hasBackground ? (size ?? 20) * 2.2 : null,
      isCircular: true,
      hasShadow: hasBackground,
      hasGradient: !hasBackground,
      onTap: onTap,
      tooltip: serviceType,
    );
  }
}

/// أيقونة حالة محسنة
class StatusIcon extends StatelessWidget {
  final IconData icon;
  final String status;
  final double? size;
  final bool isAnimated;

  const StatusIcon({
    super.key,
    required this.icon,
    required this.status,
    this.size,
    this.isAnimated = true,
  });

  @override
  Widget build(BuildContext context) {
    final color = IconColors.getStatusColor(status);

    return EnhancedIcon(
      icon: icon,
      color: color,
      size: size,
      hasGlow: isAnimated,
      isAnimated: isAnimated,
      tooltip: status,
    );
  }
}

/// أيقونة تنقل محسنة
class NavigationIcon extends StatelessWidget {
  final IconData icon;
  final String label;
  final double? size;
  final VoidCallback? onTap;
  final bool isSelected;

  const NavigationIcon({
    super.key,
    required this.icon,
    required this.label,
    this.size,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final color = isSelected
        ? IconColors.getPrimaryIconColor(isDark)
        : IconColors.settingsIcon;

    return EnhancedIcon(
      icon: icon,
      color: color,
      size: size,
      backgroundColor: isSelected ? color.withValues(alpha: 0.1) : null,
      backgroundSize: isSelected ? (size ?? 20) * 1.8 : null,
      borderRadius: BorderRadius.circular(ResponsiveHelper.smallRadius),
      onTap: onTap,
      tooltip: label,
      hasGlow: isSelected,
    );
  }
}
