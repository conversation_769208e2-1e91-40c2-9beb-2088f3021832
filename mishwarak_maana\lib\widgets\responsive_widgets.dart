import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';
import '../utils/responsive_helper.dart';

// Responsive Container
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return Container(
      width: width,
      height: height,
      alignment: alignment,
      padding: padding ?? ResponsiveHelper.cardPadding,
      margin: margin,
      decoration: decoration ??
          BoxDecoration(
            color: color ?? AppColors.surface,
            borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: ResponsiveHelper.isMobile ? 8 : 12,
                offset: Offset(0, ResponsiveHelper.isMobile ? 4 : 6),
              ),
            ],
          ),
      child: child,
    );
  }
}

// Responsive Card
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final bool showShadow;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    Widget cardWidget = Container(
      constraints: BoxConstraints(
        minHeight: ResponsiveHelper.cardMinHeight,
        maxWidth: ResponsiveHelper.cardMaxWidth,
      ),
      padding: padding ?? ResponsiveHelper.cardPadding,
      margin: margin ??
          EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.isMobile ? 8 : 12,
            vertical: ResponsiveHelper.isMobile ? 6 : 8,
          ),
      decoration: BoxDecoration(
        color: color ?? AppColors.surface,
        borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: ResponsiveHelper.isMobile ? 8 : 12,
                  offset: Offset(0, ResponsiveHelper.isMobile ? 2 : 4),
                ),
              ]
            : null,
      ),
      child: child,
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: cardWidget,
      );
    }

    return cardWidget;
  }
}

// Responsive Button
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isLoading;
  final bool isOutlined;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
    this.width,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? ResponsiveHelper.buttonHeight,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isOutlined
              ? Colors.transparent
              : (backgroundColor ?? AppColors.primary),
          foregroundColor: textColor ??
              (isOutlined ? AppColors.primary : AppColors.textOnPrimary),
          elevation: isOutlined ? 0 : (ResponsiveHelper.isMobile ? 2 : 4),
          padding: padding ??
              EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.mediumSpacing,
                vertical: ResponsiveHelper.smallSpacing,
              ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
            side: isOutlined
                ? BorderSide(
                    color: backgroundColor ?? AppColors.primary,
                    width: 2,
                  )
                : BorderSide.none,
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: ResponsiveHelper.mediumIconSize,
                height: ResponsiveHelper.mediumIconSize,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    textColor ?? AppColors.textOnPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      size: ResponsiveHelper.mediumIconSize,
                    ),
                    SizedBox(width: ResponsiveHelper.smallSpacing),
                  ],
                  Text(
                    text,
                    style: GoogleFonts.cairo(
                      fontSize: ResponsiveHelper.bodyFontSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

// Responsive Text
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final ResponsiveTextType type;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.type = ResponsiveTextType.body,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    TextStyle defaultStyle;

    switch (type) {
      case ResponsiveTextType.title:
        defaultStyle = GoogleFonts.cairo(
          fontSize: ResponsiveHelper.titleFontSize,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        );
        break;
      case ResponsiveTextType.heading:
        defaultStyle = GoogleFonts.cairo(
          fontSize: ResponsiveHelper.headingFontSize,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        );
        break;
      case ResponsiveTextType.body:
        defaultStyle = GoogleFonts.cairo(
          fontSize: ResponsiveHelper.bodyFontSize,
          color: AppColors.textPrimary,
        );
        break;
      case ResponsiveTextType.caption:
        defaultStyle = GoogleFonts.cairo(
          fontSize: ResponsiveHelper.captionFontSize,
          color: AppColors.textSecondary,
        );
        break;
    }

    return Text(
      text,
      style: style != null ? defaultStyle.merge(style) : defaultStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

enum ResponsiveTextType {
  title,
  heading,
  body,
  caption,
}

// Responsive Spacing
class ResponsiveSpacing extends StatelessWidget {
  final ResponsiveSpacingType type;
  final bool isHorizontal;

  const ResponsiveSpacing({
    super.key,
    this.type = ResponsiveSpacingType.medium,
    this.isHorizontal = false,
  });

  const ResponsiveSpacing.small({super.key, this.isHorizontal = false})
      : type = ResponsiveSpacingType.small;

  const ResponsiveSpacing.medium({super.key, this.isHorizontal = false})
      : type = ResponsiveSpacingType.medium;

  const ResponsiveSpacing.large({super.key, this.isHorizontal = false})
      : type = ResponsiveSpacingType.large;

  const ResponsiveSpacing.extraLarge({super.key, this.isHorizontal = false})
      : type = ResponsiveSpacingType.extraLarge;

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    double spacing;
    switch (type) {
      case ResponsiveSpacingType.small:
        spacing = ResponsiveHelper.smallSpacing;
        break;
      case ResponsiveSpacingType.medium:
        spacing = ResponsiveHelper.mediumSpacing;
        break;
      case ResponsiveSpacingType.large:
        spacing = ResponsiveHelper.largeSpacing;
        break;
      case ResponsiveSpacingType.extraLarge:
        spacing = ResponsiveHelper.extraLargeSpacing;
        break;
    }

    return SizedBox(
      width: isHorizontal ? spacing : null,
      height: isHorizontal ? null : spacing,
    );
  }
}

enum ResponsiveSpacingType {
  small,
  medium,
  large,
  extraLarge,
}

// Responsive Grid View
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return GridView.count(
      crossAxisCount: ResponsiveHelper.gridColumns,
      childAspectRatio: childAspectRatio ?? 1.0,
      padding: padding ?? ResponsiveHelper.screenPadding,
      mainAxisSpacing: mainAxisSpacing ?? ResponsiveHelper.mediumSpacing,
      crossAxisSpacing: crossAxisSpacing ?? ResponsiveHelper.mediumSpacing,
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }
}

// Responsive App Bar
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return AppBar(
      title: ResponsiveText(
        title,
        type: ResponsiveTextType.heading,
        style: TextStyle(
          color: foregroundColor ?? AppColors.textOnPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.textOnPrimary,
      elevation: elevation ??
          ResponsiveHelper.getResponsiveValue(
            mobile: 2,
            tablet: 4,
            desktop: 6,
          ),
      centerTitle: centerTitle,
      toolbarHeight: ResponsiveHelper.appBarHeight,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(ResponsiveHelper.appBarHeight);
}

// Responsive List Tile
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final bool dense;

  const ResponsiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    // ignore: sized_box_for_whitespace
    return Container(
      height: dense
          ? ResponsiveHelper.listTileHeight * 0.8
          : ResponsiveHelper.listTileHeight,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        contentPadding: contentPadding ??
            EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.mediumSpacing,
              vertical: ResponsiveHelper.smallSpacing,
            ),
        dense: dense,
      ),
    );
  }
}

// Responsive Dialog
class ResponsiveDialog extends StatelessWidget {
  final String? title;
  final Widget content;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? actionsPadding;

  const ResponsiveDialog({
    super.key,
    this.title,
    required this.content,
    this.actions,
    this.contentPadding,
    this.actionsPadding,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(ResponsiveHelper.mediumRadius),
      ),
      child: Container(
        width: ResponsiveHelper.dialogWidth,
        padding: contentPadding ?? ResponsiveHelper.cardPadding,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null) ...[
              ResponsiveText(
                title!,
                type: ResponsiveTextType.heading,
              ),
              ResponsiveSpacing.medium(),
            ],
            content,
            if (actions != null) ...[
              ResponsiveSpacing.medium(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions!,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Responsive Bottom Sheet
class ResponsiveBottomSheet extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool isScrollControlled;
  final EdgeInsetsGeometry? padding;

  const ResponsiveBottomSheet({
    super.key,
    required this.child,
    this.title,
    this.isScrollControlled = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveHelper.init(context);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(ResponsiveHelper.largeRadius),
          topRight: Radius.circular(ResponsiveHelper.largeRadius),
        ),
      ),
      padding: padding ?? ResponsiveHelper.screenPadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.only(bottom: ResponsiveHelper.mediumSpacing),
            decoration: BoxDecoration(
              color: AppColors.borderLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          if (title != null) ...[
            ResponsiveText(
              title!,
              type: ResponsiveTextType.heading,
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.medium(),
          ],
          child,
        ],
      ),
    );
  }
}
