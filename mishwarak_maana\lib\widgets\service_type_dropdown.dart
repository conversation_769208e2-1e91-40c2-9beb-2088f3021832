import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../models/user_model.dart';
import '../utils/responsive_helper.dart';
import 'responsive_widgets.dart';

class ServiceTypeDropdown extends StatelessWidget {
  final ServiceType? value;
  final void Function(ServiceType?)? onChanged;
  final String? Function(ServiceType?)? validator;
  final bool enabled;

  const ServiceTypeDropdown({
    super.key,
    this.value,
    this.onChanged,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        ResponsiveText(
          AppStrings.selectServiceType,
          type: ResponsiveTextType.caption,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),

        ResponsiveSpacing.small(),

        // Dropdown
        DropdownButtonFormField<ServiceType>(
          value: value,
          onChanged: enabled ? onChanged : null,
          validator: validator,
          decoration: InputDecoration(
            hintText: AppStrings.selectServiceType,
            hintStyle: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveValue(
                mobile: 16,
                tablet: 18,
                desktop: 20,
              ),
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            prefixIcon: Icon(
              Icons.work_outline,
              color: AppColors.textSecondary,
              size: ResponsiveHelper.getResponsiveValue(
                mobile: 20,
                tablet: 22,
                desktop: 24,
              ),
            ),
            filled: true,
            fillColor: enabled ? AppColors.surface : AppColors.background,
            border: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.borderLight,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.borderLight,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 2,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius:
                  BorderRadius.circular(ResponsiveHelper.mediumRadius),
              borderSide: const BorderSide(
                color: AppColors.borderLight,
                width: 1,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.mediumSpacing,
              vertical: ResponsiveHelper.mediumSpacing,
            ),
            errorStyle: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveValue(
                mobile: 12,
                tablet: 13,
                desktop: 14,
              ),
              color: AppColors.error,
              fontFamily: 'Cairo',
            ),
          ),
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveValue(
              mobile: 16,
              tablet: 18,
              desktop: 20,
            ),
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
          dropdownColor: AppColors.surface,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: AppColors.textSecondary,
            size: ResponsiveHelper.getResponsiveValue(
              mobile: 24,
              tablet: 26,
              desktop: 28,
            ),
          ),
          items: ServiceType.values.map((ServiceType type) {
            return DropdownMenuItem<ServiceType>(
              value: type,
              child: Row(
                children: [
                  // Service Type Icon
                  Text(
                    type.icon,
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getResponsiveValue(
                        mobile: 20,
                        tablet: 22,
                        desktop: 24,
                      ),
                    ),
                  ),

                  SizedBox(width: ResponsiveHelper.smallSpacing),

                  // Service Type Name
                  ResponsiveText(
                    type.displayName,
                    type: ResponsiveTextType.body,
                    style: TextStyle(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
