# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\development\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\lab\\mishwarak_maana" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\development\\flutter"
  "PROJECT_DIR=D:\\lab\\mishwarak_maana"
  "FLUTTER_ROOT=D:\\development\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\lab\\mishwarak_maana\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\lab\\mishwarak_maana"
  "FLUTTER_TARGET=D:\\lab\\mishwarak_maana\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\lab\\mishwarak_maana\\.dart_tool\\package_config.json"
)
